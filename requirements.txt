# FastAPI 核心依赖
fastapi[standard]==0.115.13
uvicorn[standard]==0.32.1

# 数据验证和序列化
pydantic==2.10.4
pydantic-settings==2.7.0

# 异步支持
asyncio-mqtt==0.16.2

# 日志增强
structlog==24.4.0
colorlog==6.9.0

# 开发和测试工具
pytest==8.3.4
pytest-asyncio==0.24.0
httpx==0.28.1

# 安全相关
PyJWT[crypto]==2.10.1
python-multipart==0.0.20

# 文件处理
aiofiles==24.1.0
pillow==11.0.0

# 数据库（PostgreSQL）
sqlalchemy==2.0.36
alembic==1.14.0
psycopg2-binary==2.9.10
asyncpg==0.30.0

# 环境变量管理
python-dotenv==1.0.1

# 时间处理
python-dateutil==2.9.0
pytz==2024.2

# JSON 处理优化
orjson==3.10.12

# 监控和指标
prometheus-client==0.21.1
psutil==6.1.0

# 请求 ID 生成
uuid==1.30
