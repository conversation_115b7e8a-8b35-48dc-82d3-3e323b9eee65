#!/usr/bin/env python3
"""
创建聊天相关表的脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from app.config import settings
from app.models.chat import ChatMessage, CarRecommendation, CarComparison
from app.database import Base
import structlog

logger = structlog.get_logger("create_chat_tables")


def create_chat_tables():
    """创建聊天相关表"""
    try:
        # 创建数据库引擎
        engine = create_engine(settings.database_url)
        
        # 创建表
        ChatMessage.__table__.create(engine, checkfirst=True)
        CarRecommendation.__table__.create(engine, checkfirst=True)
        CarComparison.__table__.create(engine, checkfirst=True)
        
        logger.info("聊天相关表创建成功")
        
        # 验证表是否创建成功
        with engine.connect() as conn:
            # 检查chat_messages表
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'chat_messages'
            """))
            
            if result.fetchone():
                logger.info("chat_messages表创建验证成功")
                
                # 显示表结构
                result = conn.execute(text("""
                    SELECT column_name, data_type, is_nullable, column_default
                    FROM information_schema.columns 
                    WHERE table_schema = 'public' 
                    AND table_name = 'chat_messages'
                    ORDER BY ordinal_position
                """))
                
                print("\n=== 聊天消息表结构 ===")
                for row in result:
                    print(f"字段: {row[0]}, 类型: {row[1]}, 可空: {row[2]}, 默认值: {row[3]}")
            else:
                logger.error("chat_messages表创建验证失败")
                return False
            
            # 检查car_recommendations表
            result = conn.execute(text("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'car_recommendations'
            """))

            if result.fetchone():
                logger.info("car_recommendations表创建验证成功")

                # 显示表结构
                result = conn.execute(text("""
                    SELECT column_name, data_type, is_nullable, column_default
                    FROM information_schema.columns
                    WHERE table_schema = 'public'
                    AND table_name = 'car_recommendations'
                    ORDER BY ordinal_position
                """))

                print("\n=== 车辆推荐表结构 ===")
                for row in result:
                    print(f"字段: {row[0]}, 类型: {row[1]}, 可空: {row[2]}, 默认值: {row[3]}")
            else:
                logger.error("car_recommendations表创建验证失败")
                return False

            # 检查car_comparisons表
            result = conn.execute(text("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = 'car_comparisons'
            """))

            if result.fetchone():
                logger.info("car_comparisons表创建验证成功")

                # 显示表结构
                result = conn.execute(text("""
                    SELECT column_name, data_type, is_nullable, column_default
                    FROM information_schema.columns
                    WHERE table_schema = 'public'
                    AND table_name = 'car_comparisons'
                    ORDER BY ordinal_position
                """))

                print("\n=== 车辆对比表结构 ===")
                for row in result:
                    print(f"字段: {row[0]}, 类型: {row[1]}, 可空: {row[2]}, 默认值: {row[3]}")
            else:
                logger.error("car_comparisons表创建验证失败")
                return False
                
        return True
        
    except Exception as e:
        logger.error("创建聊天相关表失败", error=str(e))
        return False


def check_foreign_keys():
    """检查外键约束"""
    try:
        engine = create_engine(settings.database_url)
        
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT 
                    tc.constraint_name,
                    tc.table_name,
                    kcu.column_name,
                    ccu.table_name AS foreign_table_name,
                    ccu.column_name AS foreign_column_name
                FROM information_schema.table_constraints AS tc
                JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                JOIN information_schema.constraint_column_usage AS ccu
                    ON ccu.constraint_name = tc.constraint_name
                    AND ccu.table_schema = tc.table_schema
                WHERE tc.constraint_type = 'FOREIGN KEY'
                    AND tc.table_name IN ('chat_messages', 'car_recommendations', 'car_comparisons')
            """))
            
            print("\n=== 外键约束 ===")
            for row in result:
                print(f"约束名: {row[0]}")
                print(f"表: {row[1]}.{row[2]} -> {row[3]}.{row[4]}")
                
    except Exception as e:
        logger.error("检查外键约束失败", error=str(e))


def create_indexes():
    """创建索引以提高查询性能"""
    try:
        engine = create_engine(settings.database_url)
        
        with engine.connect() as conn:
            # 为chat_messages表创建索引
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_chat_messages_conversation_created 
                ON chat_messages(conversation_id, created_at);
            """))
            
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_chat_messages_user_created 
                ON chat_messages(user_id, created_at);
            """))
            
            # 为car_recommendations表创建索引
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_car_recommendations_conversation
                ON car_recommendations(conversation_id, created_at);
            """))

            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_car_recommendations_message
                ON car_recommendations(message_id);
            """))

            # 为car_comparisons表创建索引
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_car_comparisons_conversation
                ON car_comparisons(conversation_id, created_at);
            """))

            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_car_comparisons_message
                ON car_comparisons(message_id);
            """))
            
            conn.commit()
            logger.info("索引创建成功")
            
    except Exception as e:
        logger.error("创建索引失败", error=str(e))


def run_optimization_script():
    """运行索引优化脚本"""
    print("\n🚀 运行索引优化脚本...")

    try:
        import subprocess
        import os

        # 获取优化脚本路径
        script_dir = os.path.dirname(os.path.abspath(__file__))
        optimize_script = os.path.join(script_dir, "optimize_chat_indexes.py")

        if os.path.exists(optimize_script):
            # 运行优化脚本
            result = subprocess.run([sys.executable, optimize_script],
                                  capture_output=True, text=True)

            if result.returncode == 0:
                print("✅ 索引优化完成！")
                return True
            else:
                print(f"⚠️ 索引优化脚本执行失败: {result.stderr}")
                return False
        else:
            print("⚠️ 索引优化脚本不存在，跳过优化")
            return True

    except Exception as e:
        print(f"⚠️ 运行索引优化脚本失败: {e}")
        return False


def main():
    """主函数"""
    print("开始创建聊天相关表...")

    if create_chat_tables():
        print("✅ 聊天相关表创建成功！")
        check_foreign_keys()
        create_indexes()

        # 运行索引优化
        run_optimization_script()

        print("✅ 所有操作完成！")
    else:
        print("❌ 聊天相关表创建失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
