#!/usr/bin/env python3
"""
检查 Pydantic V2 迁移脚本
检查项目中是否还有 Pydantic V1 的用法
"""

import sys
import re
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def check_pydantic_v1_usage():
    """检查 Pydantic V1 用法"""
    print("🔍 检查 Pydantic V1 用法...")
    
    # 需要检查的模式
    v1_patterns = {
        r'from pydantic import.*validator': 'validator 导入（应改为 field_validator）',
        r'@validator\s*\(': '@validator 装饰器（应改为 @field_validator）',
        r'class Config:': 'Config 类（应改为 model_config）',
        r'orm_mode\s*=\s*True': 'orm_mode（应改为 from_attributes=True）',
        r'\.dict\s*\(\s*\)': '.dict() 方法（应改为 .model_dump()）',
        r'\.json\s*\(\s*\)': '.json() 方法（应改为 .model_dump_json()）',
        r'parse_obj\s*\(': 'parse_obj 方法（应改为 model_validate）',
        r'validate_all\s*=\s*True': 'validate_all（应改为 validate_default=True）',
        r'allow_population_by_field_name\s*=\s*True': 'allow_population_by_field_name（应改为 populate_by_name=True）'
    }
    
    # 要检查的文件扩展名
    file_extensions = ['.py']
    
    # 要排除的目录
    exclude_dirs = {'__pycache__', '.git', 'venv', '.venv', 'node_modules'}
    
    issues_found = []
    
    def scan_directory(directory):
        """递归扫描目录"""
        for item in directory.iterdir():
            if item.is_dir() and item.name not in exclude_dirs:
                scan_directory(item)
            elif item.is_file() and item.suffix in file_extensions:
                scan_file(item)
    
    def scan_file(file_path):
        """扫描单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            for line_num, line in enumerate(lines, 1):
                for pattern, description in v1_patterns.items():
                    if re.search(pattern, line, re.IGNORECASE):
                        issues_found.append({
                            'file': str(file_path.relative_to(project_root)),
                            'line': line_num,
                            'content': line.strip(),
                            'issue': description,
                            'pattern': pattern
                        })
        
        except Exception as e:
            print(f"⚠️ 无法读取文件 {file_path}: {e}")
    
    # 扫描项目目录
    scan_directory(project_root / "app")
    
    # 输出结果
    if issues_found:
        print(f"\n❌ 发现 {len(issues_found)} 个 Pydantic V1 用法:")
        print("-" * 80)
        
        current_file = None
        for issue in issues_found:
            if issue['file'] != current_file:
                current_file = issue['file']
                print(f"\n📄 {current_file}:")
            
            print(f"  行 {issue['line']}: {issue['issue']}")
            print(f"    代码: {issue['content']}")
            print(f"    模式: {issue['pattern']}")
            print()
        
        print("🔧 修复建议:")
        print("1. validator → field_validator")
        print("2. class Config → model_config = ConfigDict(...)")
        print("3. orm_mode = True → from_attributes = True")
        print("4. .dict() → .model_dump()")
        print("5. .json() → .model_dump_json()")
        print("6. parse_obj() → model_validate()")
        
        return False
    else:
        print("✅ 未发现 Pydantic V1 用法！")
        print("🎉 项目已成功迁移到 Pydantic V2")
        return True


def check_imports():
    """检查导入语句"""
    print("\n🔍 检查 Pydantic 导入...")
    
    import_issues = []
    
    def scan_imports(directory):
        """扫描导入语句"""
        for item in directory.iterdir():
            if item.is_dir() and item.name not in {'__pycache__', '.git', 'venv', '.venv'}:
                scan_imports(item)
            elif item.is_file() and item.suffix == '.py':
                check_file_imports(item)
    
    def check_file_imports(file_path):
        """检查文件的导入语句"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if line.startswith('from pydantic import'):
                    # 检查是否包含 V2 的导入
                    if 'field_validator' in line or 'ConfigDict' in line:
                        print(f"✅ {file_path.relative_to(project_root)}:{line_num} - V2 导入")
                    elif 'validator' in line and 'field_validator' not in line:
                        import_issues.append({
                            'file': str(file_path.relative_to(project_root)),
                            'line': line_num,
                            'content': line,
                            'issue': '使用了 V1 的 validator 导入'
                        })
        
        except Exception as e:
            print(f"⚠️ 无法读取文件 {file_path}: {e}")
    
    scan_imports(project_root / "app")
    
    if import_issues:
        print(f"\n❌ 发现 {len(import_issues)} 个导入问题:")
        for issue in import_issues:
            print(f"  {issue['file']}:{issue['line']} - {issue['issue']}")
            print(f"    {issue['content']}")
        return False
    else:
        print("✅ 所有 Pydantic 导入都已更新到 V2")
        return True


def main():
    """主函数"""
    print("🚀 开始检查 Pydantic V2 迁移状态...")
    print("=" * 60)
    
    # 检查 V1 用法
    v1_clean = check_pydantic_v1_usage()
    
    # 检查导入
    imports_clean = check_imports()
    
    print("\n" + "=" * 60)
    print("📊 检查结果汇总")
    print("=" * 60)
    
    if v1_clean and imports_clean:
        print("🎉 恭喜！项目已成功迁移到 Pydantic V2")
        print("✅ 所有 V1 用法都已更新")
        print("✅ 所有导入都已更新")
        return True
    else:
        print("❌ 迁移未完成，请修复上述问题")
        if not v1_clean:
            print("  - 还有 Pydantic V1 用法需要修复")
        if not imports_clean:
            print("  - 还有导入语句需要更新")
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
