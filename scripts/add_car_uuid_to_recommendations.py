#!/usr/bin/env python3
"""
为 car_recommendations 表添加 car_uuid 字段的数据库迁移脚本
修复车辆ID和UUID映射关系的错误
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import text, inspect, Column, UUID
from sqlalchemy.orm import sessionmaker
from app.database import engine
from app.models.chat import CarRecommendation, CarIdMapping, Base
import uuid


def check_current_structure():
    """检查当前表结构"""
    print("🔍 检查当前 car_recommendations 表结构...")
    
    try:
        inspector = inspect(engine)
        
        if 'car_recommendations' not in inspector.get_table_names():
            print("❌ car_recommendations 表不存在")
            return False
        
        columns = inspector.get_columns('car_recommendations')
        existing_columns = [col['name'] for col in columns]
        
        print(f"📊 当前字段: {existing_columns}")
        
        if 'car_uuid' in existing_columns:
            print("✅ car_uuid 字段已存在，跳过添加步骤")
            return True
        else:
            print("ℹ️ car_uuid 字段不存在，需要添加")
            return False
            
    except Exception as e:
        print(f"❌ 检查表结构失败: {e}")
        return False


def add_car_uuid_column():
    """添加 car_uuid 字段"""
    print("🔧 为 car_recommendations 表添加 car_uuid 字段...")
    
    try:
        with engine.connect() as conn:
            # 添加 car_uuid 字段（暂时允许为空）
            add_column_sql = """
            ALTER TABLE car_recommendations 
            ADD COLUMN car_uuid UUID;
            """
            
            conn.execute(text(add_column_sql))
            conn.commit()
            
            print("✅ car_uuid 字段添加成功")
            return True
            
    except Exception as e:
        print(f"❌ 添加 car_uuid 字段失败: {e}")
        return False


def populate_car_uuid_data():
    """填充 car_uuid 数据"""
    print("📝 填充 car_uuid 数据...")

    session_local = sessionmaker(bind=engine)
    session = session_local()

    try:
        # 查询所有推荐记录
        recommendations = session.query(CarRecommendation).filter(
            CarRecommendation.is_deleted == False
        ).all()

        print(f"📊 需要处理的推荐记录数: {len(recommendations)}")

        if not recommendations:
            print("✅ 没有需要处理的记录")
            return True

        updated_count = 0
        created_mapping_count = 0

        for recommendation in recommendations:
            # 查找对应的映射记录
            mapping = session.query(CarIdMapping).filter(
                CarIdMapping.real_car_id == recommendation.car_id,
                CarIdMapping.is_deleted == False
            ).first()

            if not mapping:
                # 为缺失的 car_id 创建映射记录
                print(f"🔧 为 car_id={recommendation.car_id} 创建映射记录")
                mapping = CarIdMapping(
                    real_car_id=recommendation.car_id,
                    car_uuid=uuid.uuid4()
                )
                session.add(mapping)
                session.flush()  # 获取生成的 UUID
                created_mapping_count += 1

            # 使用 SQL 更新，避免 ORM 的字段验证问题
            update_sql = text("""
            UPDATE car_recommendations
            SET car_uuid = :car_uuid
            WHERE id = :recommendation_id
            """)

            session.execute(update_sql, {
                'car_uuid': mapping.car_uuid,
                'recommendation_id': recommendation.id
            })

            updated_count += 1

            if updated_count % 10 == 0:
                print(f"  已处理 {updated_count} 条记录...")

        # 提交更改
        session.commit()

        print(f"✅ 成功更新 {updated_count} 条记录")
        if created_mapping_count > 0:
            print(f"✅ 创建了 {created_mapping_count} 条新的映射记录")

        return True

    except Exception as e:
        session.rollback()
        print(f"❌ 填充 car_uuid 数据失败: {e}")
        return False
    finally:
        session.close()


def set_constraints():
    """设置字段约束"""
    print("🔒 设置 car_uuid 字段约束...")
    
    try:
        with engine.connect() as conn:
            # 设置非空约束
            alter_not_null_sql = """
            ALTER TABLE car_recommendations 
            ALTER COLUMN car_uuid SET NOT NULL;
            """
            
            # 添加索引
            create_index_sql = """
            CREATE INDEX IF NOT EXISTS idx_car_recommendations_car_uuid 
            ON car_recommendations (car_uuid);
            """
            
            conn.execute(text(alter_not_null_sql))
            conn.execute(text(create_index_sql))
            conn.commit()
            
            print("✅ 字段约束设置成功")
            return True
            
    except Exception as e:
        print(f"❌ 设置字段约束失败: {e}")
        return False


def verify_migration():
    """验证迁移结果"""
    print("🔍 验证迁移结果...")
    
    session_local = sessionmaker(bind=engine)
    session = session_local()
    
    try:
        # 检查字段是否存在
        inspector = inspect(engine)
        columns = inspector.get_columns('car_recommendations')
        column_names = [col['name'] for col in columns]
        
        if 'car_uuid' not in column_names:
            print("❌ car_uuid 字段不存在")
            return False
        
        # 检查数据完整性
        total_count = session.query(CarRecommendation).filter(
            CarRecommendation.is_deleted == False
        ).count()
        
        # 使用原生 SQL 查询，避免 ORM 模型问题
        result = session.execute(text("""
        SELECT COUNT(*) FROM car_recommendations 
        WHERE is_deleted = false AND car_uuid IS NOT NULL
        """)).scalar()
        
        filled_count = result or 0
        
        print(f"📊 总记录数: {total_count}")
        print(f"📊 已填充 car_uuid 的记录数: {filled_count}")
        
        if total_count == filled_count:
            print("✅ 所有记录的 car_uuid 都已正确填充")
            
            # 验证数据一致性
            inconsistent_result = session.execute(text("""
            SELECT COUNT(*) FROM car_recommendations cr
            LEFT JOIN car_id_mappings cim ON cr.car_id = cim.real_car_id AND cim.is_deleted = false
            WHERE cr.is_deleted = false AND (cim.car_uuid IS NULL OR cr.car_uuid != cim.car_uuid)
            """)).scalar()
            
            inconsistent_count = inconsistent_result or 0
            
            if inconsistent_count == 0:
                print("✅ 数据一致性验证通过")
                return True
            else:
                print(f"❌ 发现 {inconsistent_count} 条数据不一致")
                return False
        else:
            print(f"❌ 有 {total_count - filled_count} 条记录的 car_uuid 未填充")
            return False
            
    except Exception as e:
        print(f"❌ 验证迁移结果失败: {e}")
        return False
    finally:
        session.close()


def migrate_car_recommendations_table():
    """执行完整的 car_recommendations 表迁移"""
    print("🚀 开始 car_recommendations 表迁移...")
    
    # 测试数据库连接
    try:
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
            print("✅ 数据库连接成功")
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False
    
    # 检查当前结构
    if check_current_structure():
        print("ℹ️ car_uuid 字段已存在，跳过添加步骤")
    else:
        # 添加字段
        if not add_car_uuid_column():
            return False
    
    # 填充数据
    if not populate_car_uuid_data():
        return False
    
    # 设置约束
    if not set_constraints():
        print("⚠️ 设置约束失败，但数据迁移成功")
    
    # 验证结果
    if not verify_migration():
        return False
    
    print("🎉 car_recommendations 表迁移完成！")
    return True


if __name__ == "__main__":
    success = migrate_car_recommendations_table()
    if not success:
        print("❌ car_recommendations 表迁移失败！")
        sys.exit(1)
    else:
        print("✅ 迁移成功完成！")
