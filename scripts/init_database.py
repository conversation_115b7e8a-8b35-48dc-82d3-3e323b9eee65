"""
数据库初始化脚本
检测数据库结构并创建/修改表
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import inspect, text
from sqlalchemy.exc import SQLAlchemyError
import structlog

from app.database import engine, Base
from app.models.user import User, SMSCode, Province, City
from app.config import settings

logger = structlog.get_logger("database_init")


class DatabaseInitializer:
    """数据库初始化器"""
    
    def __init__(self):
        self.engine = engine
        self.inspector = inspect(engine)
    
    async def check_connection(self) -> bool:
        """检查数据库连接"""
        try:
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            logger.info("数据库连接正常")
            return True
        except Exception as e:
            logger.error("数据库连接失败", error=str(e))
            return False
    
    async def get_existing_tables(self) -> set:
        """获取现有表列表"""
        try:
            # 获取所有 schema 中的表
            tables = set()

            # 获取默认 schema 的表
            default_tables = self.inspector.get_table_names()
            tables.update(default_tables)

            # 获取 public schema 的表
            try:
                public_tables = self.inspector.get_table_names(schema='public')
                tables.update(public_tables)
            except:
                pass

            logger.info("获取现有表列表", tables=list(tables))
            return tables
        except Exception as e:
            logger.error("获取表列表失败", error=str(e))
            return set()
    
    async def create_tables(self) -> bool:
        """创建所有表"""
        try:
            # 创建所有表
            Base.metadata.create_all(bind=self.engine)
            logger.info("数据库表创建完成")
            return True
        except Exception as e:
            logger.error("创建数据库表失败", error=str(e))
            return False
    
    async def check_table_structure(self, table_name: str) -> dict:
        """检查表结构"""
        try:
            columns = self.inspector.get_columns(table_name)
            indexes = self.inspector.get_indexes(table_name)
            
            structure = {
                "columns": {col['name']: col for col in columns},
                "indexes": {idx['name']: idx for idx in indexes}
            }
            
            logger.info("表结构检查完成", table=table_name, 
                       columns=len(columns), indexes=len(indexes))
            return structure
        except Exception as e:
            logger.error("检查表结构失败", table=table_name, error=str(e))
            return {}
    
    async def verify_required_tables(self) -> bool:
        """验证必需的表是否存在"""
        required_tables = {'users', 'sms_codes', 'provinces', 'cities'}
        existing_tables = await self.get_existing_tables()
        
        missing_tables = required_tables - existing_tables
        
        if missing_tables:
            logger.warning("缺少必需的表", missing=list(missing_tables))
            return False
        
        logger.info("所有必需的表都存在")
        return True
    
    async def init_database(self) -> bool:
        """初始化数据库"""
        logger.info("开始数据库初始化")
        
        # 检查连接
        if not await self.check_connection():
            return False
        
        # 检查现有表
        existing_tables = await self.get_existing_tables()
        
        # 创建表
        if not await self.create_tables():
            return False
        
        # 验证表结构
        if not await self.verify_required_tables():
            return False
        
        logger.info("数据库初始化完成")
        return True
    
    async def repair_database(self) -> bool:
        """修复数据库结构"""
        logger.info("开始数据库修复")
        
        try:
            # 删除所有表并重新创建
            Base.metadata.drop_all(bind=self.engine)
            Base.metadata.create_all(bind=self.engine)
            
            logger.info("数据库修复完成")
            return True
        except Exception as e:
            logger.error("数据库修复失败", error=str(e))
            return False


async def init_provinces_cities():
    """初始化省市数据"""
    from sqlalchemy.orm import sessionmaker
    
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # 检查是否已有数据
        existing_provinces = session.query(Province).count()
        if existing_provinces > 0:
            logger.info("省市数据已存在，跳过初始化")
            return True
        
        # 省份数据
        provinces_data = [
            ("11", "北京市"), ("12", "天津市"), ("13", "河北省"), ("14", "山西省"),
            ("15", "内蒙古自治区"), ("21", "辽宁省"), ("22", "吉林省"), ("23", "黑龙江省"),
            ("31", "上海市"), ("32", "江苏省"), ("33", "浙江省"), ("34", "安徽省"),
            ("35", "福建省"), ("36", "江西省"), ("37", "山东省"), ("41", "河南省"),
            ("42", "湖北省"), ("43", "湖南省"), ("44", "广东省"), ("45", "广西壮族自治区"),
            ("46", "海南省"), ("50", "重庆市"), ("51", "四川省"), ("52", "贵州省"),
            ("53", "云南省"), ("54", "西藏自治区"), ("61", "陕西省"), ("62", "甘肃省"),
            ("63", "青海省"), ("64", "宁夏回族自治区"), ("65", "新疆维吾尔自治区"),
            ("71", "台湾省"), ("81", "香港特别行政区"), ("82", "澳门特别行政区")
        ]
        
        # 城市数据（示例，实际应该包含所有城市）
        cities_data = [
            ("1101", "北京市", "11"), ("1201", "天津市", "12"),
            ("1301", "石家庄市", "13"), ("1302", "唐山市", "13"), ("1303", "秦皇岛市", "13"),
            ("3101", "上海市", "31"),
            ("4401", "广州市", "44"), ("4403", "深圳市", "44"), ("4404", "珠海市", "44"),
            ("5101", "成都市", "51"), ("5102", "自贡市", "51"),
        ]
        
        # 插入省份数据
        for code, name in provinces_data:
            province = Province(code=code, name=name)
            session.add(province)
        
        # 插入城市数据
        for code, name, province_code in cities_data:
            city = City(code=code, name=name, province_code=province_code)
            session.add(city)
        
        session.commit()
        logger.info("省市数据初始化完成", 
                   provinces=len(provinces_data), 
                   cities=len(cities_data))
        return True
        
    except Exception as e:
        session.rollback()
        logger.error("省市数据初始化失败", error=str(e))
        return False
    finally:
        session.close()


async def main():
    """主函数"""
    print("🚀 开始数据库初始化...")
    
    initializer = DatabaseInitializer()
    
    # 初始化数据库
    if not await initializer.init_database():
        print("❌ 数据库初始化失败")
        return False
    
    # 初始化省市数据
    if not await init_provinces_cities():
        print("❌ 省市数据初始化失败")
        return False
    
    print("✅ 数据库初始化完成")
    return True


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="数据库初始化工具")
    parser.add_argument("--repair", action="store_true", help="修复数据库结构")
    parser.add_argument("--check", action="store_true", help="仅检查数据库状态")
    
    args = parser.parse_args()
    
    async def run():
        initializer = DatabaseInitializer()
        
        if args.check:
            print("🔍 检查数据库状态...")
            connected = await initializer.check_connection()
            tables_ok = await initializer.verify_required_tables()
            
            if connected and tables_ok:
                print("✅ 数据库状态正常")
            else:
                print("❌ 数据库状态异常")
                
        elif args.repair:
            print("🔧 修复数据库...")
            if await initializer.repair_database():
                await init_provinces_cities()
                print("✅ 数据库修复完成")
            else:
                print("❌ 数据库修复失败")
        else:
            await main()
    
    asyncio.run(run())
