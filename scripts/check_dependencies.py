#!/usr/bin/env python3
"""
依赖检查脚本
检查项目所需的所有依赖是否已安装
"""

import sys
import importlib


def check_dependencies():
    """检查项目依赖"""
    print("🔍 检查项目依赖...")
    
    # 项目所需的核心依赖
    required_packages = {
        'fastapi': 'FastAPI 核心框架',
        'uvicorn': 'ASGI 服务器',
        'pydantic': '数据验证',
        'pydantic_settings': '配置管理',
        'sqlalchemy': '数据库 ORM',
        'psycopg2': 'PostgreSQL 驱动',
        'structlog': '结构化日志',
        'jwt': 'JWT 令牌处理',
        'aiofiles': '异步文件操作',
        'psutil': '系统监控',
        'PIL': '图片处理 (Pillow)',
        'pytest': '测试框架',
        'httpx': 'HTTP 客户端',
        'alembic': '数据库迁移',
        'prometheus_client': '监控指标',
        'orjson': 'JSON 处理优化',
        'colorlog': '彩色日志',
        'python_dateutil': '时间处理',
        'dotenv': '环境变量 (python-dotenv)',
        'uuid': 'UUID 生成'
    }
    
    missing_packages = []
    installed_packages = []
    
    for package, description in required_packages.items():
        try:
            # 特殊处理一些包名映射
            import_name = package
            if package == 'PIL':
                import_name = 'PIL'
            elif package == 'jwt':
                import_name = 'jwt'
            elif package == 'dotenv':
                import_name = 'dotenv'
            elif package == 'python_dateutil':
                import_name = 'dateutil'
            elif package == 'prometheus_client':
                import_name = 'prometheus_client'
            
            importlib.import_module(import_name)
            installed_packages.append((package, description))
            print(f"✅ {package}: {description}")
            
        except ImportError:
            missing_packages.append((package, description))
            print(f"❌ {package}: {description} - 未安装")
    
    print(f"\n📊 依赖检查结果:")
    print(f"✅ 已安装: {len(installed_packages)} 个")
    print(f"❌ 缺失: {len(missing_packages)} 个")
    
    if missing_packages:
        print(f"\n🔧 需要安装的包:")
        pip_install_cmd = "pip3 install"
        
        # 包名映射到 pip 安装名
        pip_names = {
            'jwt': 'PyJWT[crypto]',
            'PIL': 'Pillow',
            'dotenv': 'python-dotenv',
            'python_dateutil': 'python-dateutil',
            'prometheus_client': 'prometheus-client'
        }
        
        for package, description in missing_packages:
            pip_name = pip_names.get(package, package)
            pip_install_cmd += f" {pip_name}"
            print(f"  - {pip_name} ({description})")
        
        print(f"\n💡 安装命令:")
        print(f"   {pip_install_cmd}")
        print(f"\n或者使用 requirements.txt:")
        print(f"   pip3 install -r requirements.txt")
        
        return False
    
    print(f"\n🎉 所有依赖都已正确安装！")
    return True


def check_import_errors():
    """检查导入错误"""
    print("\n🔍 检查项目导入...")
    
    try:
        # 测试导入主要模块
        test_imports = [
            'app.config',
            'app.database',
            'app.models.user',
            'app.schemas.auth',
            'app.schemas.user',
            'app.services.auth_service',
            'app.services.user_service',
            'app.api.v1.endpoints.health',
            'app.api.v1.endpoints.auth',
            'app.api.v1.endpoints.user'
        ]
        
        for module_name in test_imports:
            try:
                importlib.import_module(module_name)
                print(f"✅ {module_name}")
            except ImportError as e:
                print(f"❌ {module_name}: {e}")
                return False
        
        print("✅ 所有项目模块导入成功")
        return True
        
    except Exception as e:
        print(f"❌ 导入检查失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始依赖和导入检查...")
    print("=" * 50)
    
    # 检查依赖
    deps_ok = check_dependencies()
    
    if deps_ok:
        # 检查导入
        imports_ok = check_import_errors()
        
        if imports_ok:
            print("\n🎉 所有检查都通过了！项目可以正常启动。")
            return True
    
    print("\n❌ 检查未通过，请先解决上述问题。")
    return False


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
