#!/usr/bin/env python3
"""
创建用户令牌表的数据库迁移脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import text, inspect
from app.database import engine, get_db
from app.models.user import UserToken
import structlog

logger = structlog.get_logger("migration")


def create_user_tokens_table():
    """创建用户令牌表"""
    try:
        # 首先检查表是否已存在
        inspector = inspect(engine)
        existing_tables = inspector.get_table_names()

        if 'user_tokens' in existing_tables:
            logger.info("user_tokens 表已存在，检查结构...")
            # 检查字段是否完整
            existing_columns = [col['name'] for col in inspector.get_columns('user_tokens')]
            required_columns = [
                'id', 'uuid', 'user_id', 'access_token_jti', 'refresh_token_jti',
                'device_id', 'device_name', 'client_ip', 'user_agent', 'is_active',
                'is_deleted', 'access_token_expires_at', 'refresh_token_expires_at',
                'last_used_at', 'created_at', 'updated_at', 'deleted_at'
            ]

            missing_columns = set(required_columns) - set(existing_columns)
            if missing_columns:
                logger.warning(f"user_tokens 表缺少字段: {missing_columns}")
                logger.info("删除旧表并重新创建...")
                with engine.connect() as conn:
                    conn.execute(text("DROP TABLE IF EXISTS user_tokens CASCADE;"))
                    conn.commit()
            else:
                logger.info("✅ user_tokens 表结构完整，跳过创建")
                return True

        # 创建表的 SQL
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS user_tokens (
            id SERIAL PRIMARY KEY,
            uuid UUID UNIQUE NOT NULL DEFAULT gen_random_uuid(),
            user_id INTEGER NOT NULL REFERENCES users(id),
            access_token_jti VARCHAR(36) UNIQUE NOT NULL,
            refresh_token_jti VARCHAR(36) UNIQUE NOT NULL,
            device_id VARCHAR(100),
            device_name VARCHAR(200),
            client_ip VARCHAR(45),
            user_agent TEXT,
            is_active BOOLEAN NOT NULL DEFAULT true,
            is_deleted BOOLEAN NOT NULL DEFAULT false,
            access_token_expires_at TIMESTAMPTZ NOT NULL,
            refresh_token_expires_at TIMESTAMPTZ NOT NULL,
            last_used_at TIMESTAMPTZ,
            created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMPTZ,
            deleted_at TIMESTAMPTZ
        );
        """
        
        # 创建索引的 SQL
        create_indexes_sql = [
            "CREATE INDEX IF NOT EXISTS idx_user_tokens_uuid ON user_tokens(uuid);",
            "CREATE INDEX IF NOT EXISTS idx_user_tokens_user_id ON user_tokens(user_id);",
            "CREATE INDEX IF NOT EXISTS idx_user_tokens_access_jti ON user_tokens(access_token_jti);",
            "CREATE INDEX IF NOT EXISTS idx_user_tokens_refresh_jti ON user_tokens(refresh_token_jti);",
            "CREATE INDEX IF NOT EXISTS idx_user_tokens_active ON user_tokens(is_active) WHERE is_active = true;",
            "CREATE INDEX IF NOT EXISTS idx_user_tokens_deleted ON user_tokens(is_deleted) WHERE is_deleted = false;",
            "CREATE INDEX IF NOT EXISTS idx_user_tokens_device_id ON user_tokens(device_id);",
            "CREATE INDEX IF NOT EXISTS idx_user_tokens_expires_at ON user_tokens(access_token_expires_at, refresh_token_expires_at);"
        ]
        
        # 创建触发器的 SQL（自动更新 updated_at）
        create_trigger_sql = """
        CREATE OR REPLACE FUNCTION update_user_tokens_updated_at()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = NOW();
            RETURN NEW;
        END;
        $$ language 'plpgsql';
        
        DROP TRIGGER IF EXISTS trigger_user_tokens_updated_at ON user_tokens;
        CREATE TRIGGER trigger_user_tokens_updated_at
            BEFORE UPDATE ON user_tokens
            FOR EACH ROW
            EXECUTE FUNCTION update_user_tokens_updated_at();
        """
        
        with engine.connect() as conn:
            # 开始事务
            trans = conn.begin()
            
            try:
                # 创建表
                logger.info("创建 user_tokens 表...")
                conn.execute(text(create_table_sql))
                
                # 创建索引
                logger.info("创建索引...")
                for index_sql in create_indexes_sql:
                    conn.execute(text(index_sql))
                
                # 创建触发器
                logger.info("创建触发器...")
                conn.execute(text(create_trigger_sql))
                
                # 提交事务
                trans.commit()
                logger.info("✅ user_tokens 表创建成功！")
                
                return True
                
            except Exception as e:
                # 回滚事务
                trans.rollback()
                logger.error("❌ 创建表失败", error=str(e))
                raise
                
    except Exception as e:
        logger.error("❌ 数据库连接失败", error=str(e))
        return False


def verify_table_creation():
    """验证表是否创建成功"""
    try:
        verify_sql = """
        SELECT 
            table_name,
            column_name,
            data_type,
            is_nullable,
            column_default
        FROM information_schema.columns 
        WHERE table_name = 'user_tokens'
        ORDER BY ordinal_position;
        """
        
        with engine.connect() as conn:
            result = conn.execute(text(verify_sql))
            columns = result.fetchall()
            
            if columns:
                logger.info("✅ 表结构验证:")
                for col in columns:
                    logger.info(f"  - {col.column_name}: {col.data_type} {'NULL' if col.is_nullable == 'YES' else 'NOT NULL'}")
                
                # 验证索引
                index_sql = """
                SELECT indexname, indexdef 
                FROM pg_indexes 
                WHERE tablename = 'user_tokens';
                """
                
                result = conn.execute(text(index_sql))
                indexes = result.fetchall()
                
                logger.info("✅ 索引验证:")
                for idx in indexes:
                    logger.info(f"  - {idx.indexname}")
                
                return True
            else:
                logger.error("❌ 表不存在或创建失败")
                return False
                
    except Exception as e:
        logger.error("❌ 表验证失败", error=str(e))
        return False


def check_foreign_key():
    """检查外键约束"""
    try:
        fk_sql = """
        SELECT 
            tc.constraint_name,
            tc.table_name,
            kcu.column_name,
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
            AND tc.table_name = 'user_tokens';
        """
        
        with engine.connect() as conn:
            result = conn.execute(text(fk_sql))
            foreign_keys = result.fetchall()
            
            if foreign_keys:
                logger.info("✅ 外键约束验证:")
                for fk in foreign_keys:
                    logger.info(f"  - {fk.column_name} -> {fk.foreign_table_name}.{fk.foreign_column_name}")
                return True
            else:
                logger.warning("⚠️ 未找到外键约束")
                return False
                
    except Exception as e:
        logger.error("❌ 外键验证失败", error=str(e))
        return False


def main():
    """主函数"""
    print("🚀 开始创建用户令牌表...")
    print("=" * 60)
    
    # 创建表
    if create_user_tokens_table():
        print("\n📊 验证表创建结果...")
        
        # 验证表结构
        table_ok = verify_table_creation()
        
        # 验证外键
        fk_ok = check_foreign_key()
        
        print("\n" + "=" * 60)
        print("📊 创建结果汇总")
        print("=" * 60)
        
        if table_ok:
            print("🎉 用户令牌表创建成功！")
            print("✅ 表结构正确")
            print("✅ 索引创建完成")
            print("✅ 触发器创建完成")
            
            if fk_ok:
                print("✅ 外键约束正确")
            else:
                print("⚠️ 外键约束可能有问题，请检查")
            
            print("\n🔧 下一步操作:")
            print("1. 重新启动应用服务器")
            print("2. 测试登录功能")
            print("3. 测试令牌刷新功能")
            print("4. 测试登出功能")
            
            return True
        else:
            print("❌ 表创建验证失败")
            return False
    else:
        print("❌ 表创建失败")
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
