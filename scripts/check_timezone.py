#!/usr/bin/env python3
"""
时区检查脚本
检查项目中是否所有时间都使用中国时区（UTC+8）
"""

import sys
import re
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def check_timezone_usage():
    """检查时区使用情况"""
    print("🔍 检查时区使用情况...")
    
    # 需要检查的问题模式
    problematic_patterns = {
        r'datetime\.now\(\)': 'datetime.now() 没有时区信息（应使用 get_china_now()）',
        r'datetime\.utcnow\(\)': 'datetime.utcnow() 使用 UTC（应使用 get_china_now()）',
        r'timezone\.utc': 'timezone.utc 使用 UTC（应使用中国时区）',
        r'time\.time\(\)': 'time.time() 可能不是中国时区（应使用 get_china_timestamp()）',
        r'datetime\.fromtimestamp\([^,)]+\)': 'datetime.fromtimestamp() 没有时区（应使用 from_timestamp_to_china()）',
        r'\.timestamp\(\)': '.timestamp() 可能不是中国时区（检查是否需要转换）'
    }
    
    # 正确的模式（不报告为问题）
    good_patterns = [
        r'get_china_now\(\)',
        r'get_china_timestamp\(\)',
        r'to_china_time\(',
        r'to_utc_for_db\(',
        r'get_expiry_time\(',
        r'is_expired\(',
        r'from_timestamp_to_china\(',
        r'CHINA_TZ',
        r'UTC_TZ'
    ]
    
    # 要检查的文件扩展名
    file_extensions = ['.py']
    
    # 要排除的目录
    exclude_dirs = {'__pycache__', '.git', 'venv', '.venv', 'node_modules', 'scripts'}
    
    issues_found = []
    good_usage_found = []
    
    def scan_directory(directory):
        """递归扫描目录"""
        for item in directory.iterdir():
            if item.is_dir() and item.name not in exclude_dirs:
                scan_directory(item)
            elif item.is_file() and item.suffix in file_extensions:
                scan_file(item)
    
    def scan_file(file_path):
        """扫描单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            for line_num, line in enumerate(lines, 1):
                # 检查问题模式
                for pattern, description in problematic_patterns.items():
                    if re.search(pattern, line):
                        # 检查是否在注释中
                        if line.strip().startswith('#'):
                            continue
                        
                        # 检查是否是正确的用法
                        is_good_usage = False
                        for good_pattern in good_patterns:
                            if re.search(good_pattern, line):
                                is_good_usage = True
                                break
                        
                        if not is_good_usage:
                            issues_found.append({
                                'file': str(file_path.relative_to(project_root)),
                                'line': line_num,
                                'content': line.strip(),
                                'issue': description,
                                'pattern': pattern
                            })
                
                # 检查正确的用法
                for good_pattern in good_patterns:
                    if re.search(good_pattern, line):
                        good_usage_found.append({
                            'file': str(file_path.relative_to(project_root)),
                            'line': line_num,
                            'content': line.strip(),
                            'pattern': good_pattern
                        })
        
        except Exception as e:
            print(f"⚠️ 无法读取文件 {file_path}: {e}")
    
    # 扫描项目目录
    scan_directory(project_root / "app")
    
    # 输出结果
    print(f"\n📊 时区检查结果:")
    print(f"✅ 正确的时区用法: {len(good_usage_found)} 处")
    print(f"❌ 可能的问题: {len(issues_found)} 处")
    
    if good_usage_found:
        print(f"\n✅ 正确的时区用法:")
        current_file = None
        for usage in good_usage_found[:10]:  # 只显示前10个
            if usage['file'] != current_file:
                current_file = usage['file']
                print(f"\n📄 {current_file}:")
            print(f"  行 {usage['line']}: {usage['content']}")
        
        if len(good_usage_found) > 10:
            print(f"  ... 还有 {len(good_usage_found) - 10} 处正确用法")
    
    if issues_found:
        print(f"\n❌ 发现 {len(issues_found)} 个可能的时区问题:")
        print("-" * 80)
        
        current_file = None
        for issue in issues_found:
            if issue['file'] != current_file:
                current_file = issue['file']
                print(f"\n📄 {current_file}:")
            
            print(f"  行 {issue['line']}: {issue['issue']}")
            print(f"    代码: {issue['content']}")
            print()
        
        print("🔧 修复建议:")
        print("1. datetime.now() → get_china_now()")
        print("2. datetime.utcnow() → get_china_now()")
        print("3. time.time() → get_china_timestamp()")
        print("4. 数据库存储时间 → to_utc_for_db()")
        print("5. 检查过期时间 → is_expired()")
        
        return False
    else:
        print("🎉 所有时间处理都使用了中国时区！")
        return True


def test_timezone_functions():
    """测试时区函数"""
    print("\n🧪 测试时区函数...")
    
    try:
        from app.utils.timezone import (
            get_china_now, get_china_timestamp, to_china_time,
            to_utc_for_db, format_china_time, get_expiry_time,
            is_expired, CHINA_TZ
        )
        from datetime import datetime
        import pytz
        
        # 测试获取中国时间
        china_now = get_china_now()
        print(f"✅ 中国当前时间: {china_now}")
        print(f"   时区: {china_now.tzinfo}")
        
        # 测试时间戳
        china_timestamp = get_china_timestamp()
        print(f"✅ 中国时间戳: {china_timestamp}")
        
        # 测试数据库时间
        db_time = to_utc_for_db()
        print(f"✅ 数据库存储时间: {db_time}")
        print(f"   时区: {db_time.tzinfo}")
        
        # 测试过期时间
        expiry = get_expiry_time(5)
        print(f"✅ 5分钟后过期时间: {expiry}")
        
        # 验证时区正确性
        if china_now.tzinfo.zone == 'Asia/Shanghai':
            print("✅ 时区设置正确: Asia/Shanghai")
        else:
            print(f"❌ 时区设置错误: {china_now.tzinfo}")
            return False
        
        # 验证时差
        utc_now = datetime.now(pytz.UTC)
        china_offset = china_now.utcoffset().total_seconds() / 3600
        if china_offset == 8.0:
            print("✅ 时差正确: UTC+8")
        else:
            print(f"❌ 时差错误: UTC+{china_offset}")
            return False
        
        print("🎉 所有时区函数测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 时区函数测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始检查项目时区使用情况...")
    print("=" * 60)
    
    # 检查时区使用
    usage_clean = check_timezone_usage()
    
    # 测试时区函数
    functions_ok = test_timezone_functions()
    
    print("\n" + "=" * 60)
    print("📊 检查结果汇总")
    print("=" * 60)
    
    if usage_clean and functions_ok:
        print("🎉 恭喜！项目已正确使用中国时区（UTC+8）")
        print("✅ 所有时间处理都符合要求")
        print("✅ 时区函数工作正常")
        return True
    else:
        print("❌ 时区使用检查未通过，请修复上述问题")
        if not usage_clean:
            print("  - 还有时区使用问题需要修复")
        if not functions_ok:
            print("  - 时区函数有问题")
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
