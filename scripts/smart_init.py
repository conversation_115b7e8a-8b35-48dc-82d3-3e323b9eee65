#!/usr/bin/env python3
"""
智能数据库初始化脚本
检查数据库结构，只在需要时才重建
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import text, inspect
from sqlalchemy.orm import sessionmaker
from app.database import engine
from app.models.user import User, SMSCode, Province, City, UserToken, Base
from app.models.conversation import AIConversation
from app.models.chat import ChatMessage, CarRecommendation, CarComparison


def check_table_structure():
    """检查表结构是否符合要求"""
    print("🔍 检查数据库表结构...")
    
    try:
        inspector = inspect(engine)
        
        # 需要的表和字段
        required_tables = {
            'users': ['id', 'uuid', 'phone', 'username', 'avatar_url', 'province_code', 'city_code',
                     'is_active', 'is_deleted', 'created_at', 'updated_at', 'deleted_at', 'last_login'],
            'sms_codes': ['id', 'uuid', 'phone', 'code', 'purpose', 'is_used', 'is_deleted',
                         'expires_at', 'created_at', 'updated_at', 'deleted_at'],
            'provinces': ['id', 'uuid', 'code', 'name', 'is_active', 'is_deleted',
                         'created_at', 'updated_at', 'deleted_at'],
            'cities': ['id', 'uuid', 'code', 'name', 'province_code', 'is_active', 'is_deleted',
                      'created_at', 'updated_at', 'deleted_at'],
            'user_tokens': ['id', 'uuid', 'user_id', 'access_token_jti', 'refresh_token_jti',
                           'device_id', 'device_name', 'client_ip', 'user_agent', 'is_active', 'is_deleted',
                           'access_token_expires_at', 'refresh_token_expires_at', 'last_used_at',
                           'created_at', 'updated_at', 'deleted_at'],
            'ai_conversations': ['id', 'uuid', 'user_id', 'title', 'is_deleted',
                               'created_at', 'updated_at', 'deleted_at'],
            'chat_messages': ['id', 'uuid', 'conversation_id', 'user_id', 'message_type', 'content',
                            'message_id', 'is_complete', 'is_deleted', 'created_at', 'updated_at', 'deleted_at'],
            'car_recommendations': ['id', 'uuid', 'message_id', 'car_name', 'car_brand', 'car_model',
                                  'price', 'image_url', 'description', 'features', 'is_deleted',
                                  'created_at', 'updated_at', 'deleted_at'],
            'car_comparisons': ['id', 'uuid', 'message_id', 'car_a_name', 'car_b_name', 'comparison_data',
                              'is_deleted', 'created_at', 'updated_at', 'deleted_at']
        }
        
        # 检查表是否存在
        existing_tables = set(inspector.get_table_names())
        required_table_names = set(required_tables.keys())
        
        if not required_table_names.issubset(existing_tables):
            missing_tables = required_table_names - existing_tables
            print(f"❌ 缺少表: {missing_tables}")
            return False
        
        # 检查每个表的字段
        for table_name, required_columns in required_tables.items():
            existing_columns = [col['name'] for col in inspector.get_columns(table_name)]
            missing_columns = set(required_columns) - set(existing_columns)
            
            if missing_columns:
                print(f"❌ 表 {table_name} 缺少字段: {missing_columns}")
                return False
            
            print(f"✅ 表 {table_name} 结构正确")
        
        print("✅ 所有表结构都符合要求")
        return True
        
    except Exception as e:
        print(f"❌ 检查表结构失败: {e}")
        return False


def check_data_integrity():
    """检查数据完整性"""
    print("🔍 检查数据完整性...")
    
    session_local = sessionmaker(bind=engine)
    session = session_local()
    
    try:
        # 检查省市数据
        province_count = session.query(Province).filter(
            Province.is_deleted == False
        ).count()
        
        city_count = session.query(City).filter(
            City.is_deleted == False
        ).count()
        
        print(f"📊 省份数据: {province_count} 条")
        print(f"📊 城市数据: {city_count} 条")
        
        if province_count == 0:
            print("❌ 缺少省份数据")
            return False
        
        if city_count == 0:
            print("❌ 缺少城市数据")
            return False
        
        print("✅ 数据完整性检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据完整性检查失败: {e}")
        return False
    finally:
        session.close()


def init_provinces_cities():
    """初始化省市数据"""
    print("🌍 初始化省市数据...")
    
    session_local = sessionmaker(bind=engine)
    session = session_local()
    
    try:
        # 检查是否已有数据
        province_count = session.query(Province).filter(Province.is_deleted == False).count()
        city_count = session.query(City).filter(City.is_deleted == False).count()

        if province_count >= 30 and city_count >= 300:  # 检查是否有足够的完整数据
            print(f"✅ 省市数据已存在 (省份: {province_count}, 城市: {city_count})")
            return True

        print("📥 数据不足，开始完整初始化...")
        session.close()  # 关闭当前会话

        # 使用完整的省市数据初始化脚本
        try:
            from scripts.init_provinces_cities import main as init_full_data
            return init_full_data()
        except Exception as e:
            print(f"⚠️ 完整初始化失败: {e}")
            print("🔄 使用基础数据作为备选...")
            # 重新打开会话进行基础初始化
            session = session_local()
        
        # 省份数据
        provinces_data = [
            ("11", "北京市"), ("12", "天津市"), ("13", "河北省"), ("14", "山西省"),
            ("15", "内蒙古自治区"), ("21", "辽宁省"), ("22", "吉林省"), ("23", "黑龙江省"),
            ("31", "上海市"), ("32", "江苏省"), ("33", "浙江省"), ("34", "安徽省"),
            ("35", "福建省"), ("36", "江西省"), ("37", "山东省"), ("41", "河南省"),
            ("42", "湖北省"), ("43", "湖南省"), ("44", "广东省"), ("45", "广西壮族自治区"),
            ("46", "海南省"), ("50", "重庆市"), ("51", "四川省"), ("52", "贵州省"),
            ("53", "云南省"), ("54", "西藏自治区"), ("61", "陕西省"), ("62", "甘肃省"),
            ("63", "青海省"), ("64", "宁夏回族自治区"), ("65", "新疆维吾尔自治区"),
            ("71", "台湾省"), ("81", "香港特别行政区"), ("82", "澳门特别行政区")
        ]
        
        # 城市数据（示例）
        cities_data = [
            ("1101", "北京市", "11"), ("1201", "天津市", "12"),
            ("1301", "石家庄市", "13"), ("1302", "唐山市", "13"), ("1303", "秦皇岛市", "13"),
            ("1401", "太原市", "14"), ("1402", "大同市", "14"),
            ("1501", "呼和浩特市", "15"), ("1502", "包头市", "15"),
            ("2101", "沈阳市", "21"), ("2102", "大连市", "21"),
            ("2201", "长春市", "22"), ("2202", "吉林市", "22"),
            ("2301", "哈尔滨市", "23"), ("2302", "齐齐哈尔市", "23"),
            ("3101", "上海市", "31"),
            ("3201", "南京市", "32"), ("3202", "无锡市", "32"), ("3203", "徐州市", "32"),
            ("3301", "杭州市", "33"), ("3302", "宁波市", "33"),
            ("3401", "合肥市", "34"), ("3402", "芜湖市", "34"),
            ("3501", "福州市", "35"), ("3502", "厦门市", "35"),
            ("3601", "南昌市", "36"), ("3602", "景德镇市", "36"),
            ("3701", "济南市", "37"), ("3702", "青岛市", "37"),
            ("4101", "郑州市", "41"), ("4102", "开封市", "41"),
            ("4201", "武汉市", "42"), ("4202", "黄石市", "42"),
            ("4301", "长沙市", "43"), ("4302", "株洲市", "43"),
            ("4401", "广州市", "44"), ("4403", "深圳市", "44"), ("4404", "珠海市", "44"),
            ("4501", "南宁市", "45"), ("4502", "柳州市", "45"),
            ("4601", "海口市", "46"), ("4602", "三亚市", "46"),
            ("5001", "重庆市", "50"),
            ("5101", "成都市", "51"), ("5102", "自贡市", "51"),
            ("5201", "贵阳市", "52"), ("5202", "六盘水市", "52"),
            ("5301", "昆明市", "53"), ("5302", "曲靖市", "53"),
            ("5401", "拉萨市", "54"),
            ("6101", "西安市", "61"), ("6102", "铜川市", "61"),
            ("6201", "兰州市", "62"), ("6202", "嘉峪关市", "62"),
            ("6301", "西宁市", "63"),
            ("6401", "银川市", "64"),
            ("6501", "乌鲁木齐市", "65"),
            ("7101", "台北市", "71"),
            ("8101", "香港", "81"),
            ("8201", "澳门", "82")
        ]
        
        # 插入省份数据
        for code, name in provinces_data:
            province = Province(code=code, name=name, is_active=True)
            session.add(province)
        
        # 插入城市数据
        for code, name, province_code in cities_data:
            city = City(code=code, name=name, province_code=province_code, is_active=True)
            session.add(city)
        
        session.commit()
        print(f"✅ 省市数据初始化完成 (省份: {len(provinces_data)}, 城市: {len(cities_data)})")
        return True
        
    except Exception as e:
        session.rollback()
        print(f"❌ 省市数据初始化失败: {e}")
        return False
    finally:
        session.close()


def create_all_tables():
    """创建所有表"""
    print("🔧 创建数据库表...")

    try:
        # 删除所有表（如果存在）
        Base.metadata.drop_all(bind=engine)
        print("🗑️ 已删除旧表")

        # 创建所有表
        Base.metadata.create_all(bind=engine)
        print("✅ 所有表创建成功")

        # 验证表创建
        inspector = inspect(engine)
        created_tables = set(inspector.get_table_names())
        expected_tables = {'users', 'sms_codes', 'provinces', 'cities', 'user_tokens'}

        if expected_tables.issubset(created_tables):
            print("✅ 表结构验证通过")
            return True
        else:
            missing = expected_tables - created_tables
            print(f"❌ 缺少表: {missing}")
            return False

    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        return False


def smart_init_database():
    """智能数据库初始化"""
    print("🚀 开始智能数据库初始化...")
    
    # 测试数据库连接
    try:
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
            print("✅ 数据库连接成功")
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False
    
    # 检查表结构
    structure_ok = check_table_structure()
    
    if not structure_ok:
        print("🔧 表结构不符合要求，需要重建...")
        if not create_all_tables():
            return False
    
    # 检查数据完整性
    data_ok = check_data_integrity()
    
    if not data_ok:
        print("🌍 数据不完整，需要初始化...")
        if not init_provinces_cities():
            return False
    
    print("🎉 数据库初始化完成！")
    return True


if __name__ == "__main__":
    success = smart_init_database()
    if not success:
        print("❌ 数据库初始化失败！")
        sys.exit(1)
