#!/usr/bin/env python3
"""
创建车辆ID映射表的数据库迁移脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import text, inspect
from sqlalchemy.orm import sessionmaker
from app.database import engine
from app.models.chat import CarIdMapping, Base


def check_table_exists():
    """检查表是否已存在"""
    print("🔍 检查车辆ID映射表是否存在...")
    
    try:
        inspector = inspect(engine)
        existing_tables = inspector.get_table_names()
        
        if 'car_id_mappings' in existing_tables:
            print("✅ 车辆ID映射表已存在")
            return True
        else:
            print("ℹ️ 车辆ID映射表不存在，需要创建")
            return False
            
    except Exception as e:
        print(f"❌ 检查表存在性失败: {e}")
        return False


def create_car_mapping_table():
    """创建车辆ID映射表"""
    print("🔧 正在创建车辆ID映射表...")
    
    try:
        # 创建表
        CarIdMapping.__table__.create(bind=engine, checkfirst=True)
        print("✅ 车辆ID映射表创建成功")
        
        # 验证表结构
        inspector = inspect(engine)
        columns = inspector.get_columns('car_id_mappings')
        indexes = inspector.get_indexes('car_id_mappings')
        
        print(f"✅ 表字段数量: {len(columns)}")
        print(f"✅ 索引数量: {len(indexes)}")
        
        # 显示字段信息
        print("📋 表字段信息:")
        for col in columns:
            print(f"  - {col['name']}: {col['type']}")
        
        # 显示索引信息
        print("📋 索引信息:")
        for idx in indexes:
            print(f"  - {idx['name']}: {idx['column_names']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建车辆ID映射表失败: {e}")
        return False


def create_additional_indexes():
    """创建额外的性能优化索引"""
    print("🚀 正在创建性能优化索引...")
    
    try:
        with engine.connect() as conn:
            # 创建复合索引以优化查询性能
            indexes_to_create = [
                # 用于快速查找未删除的映射记录
                "CREATE INDEX IF NOT EXISTS idx_car_mappings_active ON car_id_mappings (is_deleted, real_car_id) WHERE is_deleted = false",
                # 用于按使用频率排序
                "CREATE INDEX IF NOT EXISTS idx_car_mappings_usage ON car_id_mappings (usage_count DESC, last_used_at DESC)",
                # 用于时间范围查询
                "CREATE INDEX IF NOT EXISTS idx_car_mappings_time_range ON car_id_mappings (created_at, last_used_at)"
            ]
            
            for index_sql in indexes_to_create:
                try:
                    conn.execute(text(index_sql))
                    print(f"✅ 索引创建成功: {index_sql.split('idx_')[1].split(' ')[0]}")
                except Exception as e:
                    print(f"⚠️ 索引创建失败: {e}")
            
            conn.commit()
            print("✅ 所有性能优化索引创建完成")
            return True
            
    except Exception as e:
        print(f"❌ 创建性能优化索引失败: {e}")
        return False


def verify_table_structure():
    """验证表结构是否正确"""
    print("🔍 验证表结构...")
    
    try:
        inspector = inspect(engine)
        
        # 检查必需的字段
        required_columns = [
            'id', 'uuid', 'real_car_id', 'car_uuid', 
            'first_seen_at', 'last_used_at', 'usage_count',
            'is_deleted', 'created_at', 'updated_at', 'deleted_at'
        ]
        
        columns = inspector.get_columns('car_id_mappings')
        existing_columns = [col['name'] for col in columns]
        
        missing_columns = set(required_columns) - set(existing_columns)
        if missing_columns:
            print(f"❌ 缺少必需字段: {missing_columns}")
            return False
        
        # 检查唯一约束
        indexes = inspector.get_indexes('car_id_mappings')
        unique_indexes = [idx for idx in indexes if idx.get('unique', False)]
        
        print(f"✅ 表结构验证通过")
        print(f"✅ 字段数量: {len(existing_columns)}")
        print(f"✅ 唯一索引数量: {len(unique_indexes)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 表结构验证失败: {e}")
        return False


def migrate_car_mapping_table():
    """执行完整的车辆ID映射表迁移"""
    print("🚀 开始车辆ID映射表迁移...")
    
    # 测试数据库连接
    try:
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
            print("✅ 数据库连接成功")
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False
    
    # 检查表是否已存在
    if check_table_exists():
        print("ℹ️ 表已存在，跳过创建步骤")
    else:
        # 创建表
        if not create_car_mapping_table():
            return False
    
    # 创建性能优化索引
    if not create_additional_indexes():
        print("⚠️ 性能优化索引创建失败，但表创建成功")
    
    # 验证表结构
    if not verify_table_structure():
        return False
    
    print("🎉 车辆ID映射表迁移完成！")
    return True


if __name__ == "__main__":
    success = migrate_car_mapping_table()
    if not success:
        print("❌ 车辆ID映射表迁移失败！")
        sys.exit(1)
    else:
        print("✅ 迁移成功完成！")
