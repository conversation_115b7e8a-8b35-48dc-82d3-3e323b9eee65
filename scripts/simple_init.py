#!/usr/bin/env python3
"""
简化的数据库初始化脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import text
from app.database import engine
from app.models.user import User, SMSCode, Province, City, Base


def init_database():
    """初始化数据库"""
    print("🔧 正在初始化数据库...")
    
    try:
        # 测试数据库连接
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print("✅ 数据库连接成功")
        
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        print("✅ 数据库表创建完成")
        
        # 检查表是否存在
        with engine.connect() as conn:
            # 检查表是否存在
            tables_check = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name IN ('users', 'sms_codes', 'provinces', 'cities')
            """))
            
            existing_tables = [row[0] for row in tables_check]
            print(f"✅ 已创建的表: {existing_tables}")
            
            if len(existing_tables) == 4:
                print("✅ 所有必需的表都已创建")
                
                # 初始化省市数据
                init_provinces_cities()
                return True
            else:
                print(f"❌ 缺少表: {set(['users', 'sms_codes', 'provinces', 'cities']) - set(existing_tables)}")
                return False
                
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False


def init_provinces_cities():
    """初始化省市数据"""
    from sqlalchemy.orm import sessionmaker
    
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # 检查是否已有数据
        existing_count = session.query(Province).count()
        if existing_count > 0:
            print("✅ 省市数据已存在")
            return True
        
        print("🌍 正在初始化省市数据...")
        
        # 省份数据
        provinces_data = [
            ("11", "北京市"), ("12", "天津市"), ("13", "河北省"), ("14", "山西省"),
            ("15", "内蒙古自治区"), ("21", "辽宁省"), ("22", "吉林省"), ("23", "黑龙江省"),
            ("31", "上海市"), ("32", "江苏省"), ("33", "浙江省"), ("34", "安徽省"),
            ("35", "福建省"), ("36", "江西省"), ("37", "山东省"), ("41", "河南省"),
            ("42", "湖北省"), ("43", "湖南省"), ("44", "广东省"), ("45", "广西壮族自治区"),
            ("46", "海南省"), ("50", "重庆市"), ("51", "四川省"), ("52", "贵州省"),
            ("53", "云南省"), ("54", "西藏自治区"), ("61", "陕西省"), ("62", "甘肃省"),
            ("63", "青海省"), ("64", "宁夏回族自治区"), ("65", "新疆维吾尔自治区"),
            ("71", "台湾省"), ("81", "香港特别行政区"), ("82", "澳门特别行政区")
        ]
        
        # 城市数据（示例）
        cities_data = [
            ("1101", "北京市", "11"), ("1201", "天津市", "12"),
            ("1301", "石家庄市", "13"), ("1302", "唐山市", "13"), ("1303", "秦皇岛市", "13"),
            ("1401", "太原市", "14"), ("1402", "大同市", "14"),
            ("1501", "呼和浩特市", "15"), ("1502", "包头市", "15"),
            ("2101", "沈阳市", "21"), ("2102", "大连市", "21"),
            ("2201", "长春市", "22"), ("2202", "吉林市", "22"),
            ("2301", "哈尔滨市", "23"), ("2302", "齐齐哈尔市", "23"),
            ("3101", "上海市", "31"),
            ("3201", "南京市", "32"), ("3202", "无锡市", "32"), ("3203", "徐州市", "32"),
            ("3301", "杭州市", "33"), ("3302", "宁波市", "33"),
            ("3401", "合肥市", "34"), ("3402", "芜湖市", "34"),
            ("3501", "福州市", "35"), ("3502", "厦门市", "35"),
            ("3601", "南昌市", "36"), ("3602", "景德镇市", "36"),
            ("3701", "济南市", "37"), ("3702", "青岛市", "37"),
            ("4101", "郑州市", "41"), ("4102", "开封市", "41"),
            ("4201", "武汉市", "42"), ("4202", "黄石市", "42"),
            ("4301", "长沙市", "43"), ("4302", "株洲市", "43"),
            ("4401", "广州市", "44"), ("4403", "深圳市", "44"), ("4404", "珠海市", "44"),
            ("4501", "南宁市", "45"), ("4502", "柳州市", "45"),
            ("4601", "海口市", "46"), ("4602", "三亚市", "46"),
            ("5001", "重庆市", "50"),
            ("5101", "成都市", "51"), ("5102", "自贡市", "51"),
            ("5201", "贵阳市", "52"), ("5202", "六盘水市", "52"),
            ("5301", "昆明市", "53"), ("5302", "曲靖市", "53"),
            ("5401", "拉萨市", "54"),
            ("6101", "西安市", "61"), ("6102", "铜川市", "61"),
            ("6201", "兰州市", "62"), ("6202", "嘉峪关市", "62"),
            ("6301", "西宁市", "63"),
            ("6401", "银川市", "64"),
            ("6501", "乌鲁木齐市", "65"),
            ("7101", "台北市", "71"),
            ("8101", "香港", "81"),
            ("8201", "澳门", "82")
        ]
        
        # 插入省份数据
        for code, name in provinces_data:
            province = Province(code=code, name=name, is_active=True)
            session.add(province)

        # 插入城市数据
        for code, name, province_code in cities_data:
            city = City(code=code, name=name, province_code=province_code, is_active=True)
            session.add(city)
        
        session.commit()
        print(f"✅ 省市数据初始化完成 (省份: {len(provinces_data)}, 城市: {len(cities_data)})")
        return True
        
    except Exception as e:
        session.rollback()
        print(f"❌ 省市数据初始化失败: {e}")
        return False
    finally:
        session.close()


if __name__ == "__main__":
    success = init_database()
    if success:
        print("🎉 数据库初始化完成！")
    else:
        print("❌ 数据库初始化失败！")
        sys.exit(1)
