#!/usr/bin/env python3
"""
数据库修复脚本
自动修复数据库结构问题
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import text, inspect
from app.database import engine
from app.models.user import Base
import structlog

logger = structlog.get_logger("db_fix")


def backup_data():
    """备份重要数据"""
    print("💾 备份重要数据...")
    
    backup_data = {}
    
    try:
        with engine.connect() as conn:
            # 备份用户数据
            try:
                result = conn.execute(text("SELECT * FROM users WHERE is_deleted = false"))
                backup_data['users'] = [dict(row._mapping) for row in result]
                print(f"  ✅ 备份用户数据: {len(backup_data['users'])} 条")
            except Exception as e:
                print(f"  ⚠️ 用户表不存在或备份失败: {e}")
                backup_data['users'] = []
            
            # 备份省份数据
            try:
                result = conn.execute(text("SELECT * FROM provinces WHERE is_deleted = false"))
                backup_data['provinces'] = [dict(row._mapping) for row in result]
                print(f"  ✅ 备份省份数据: {len(backup_data['provinces'])} 条")
            except Exception as e:
                print(f"  ⚠️ 省份表不存在或备份失败: {e}")
                backup_data['provinces'] = []
            
            # 备份城市数据
            try:
                result = conn.execute(text("SELECT * FROM cities WHERE is_deleted = false"))
                backup_data['cities'] = [dict(row._mapping) for row in result]
                print(f"  ✅ 备份城市数据: {len(backup_data['cities'])} 条")
            except Exception as e:
                print(f"  ⚠️ 城市表不存在或备份失败: {e}")
                backup_data['cities'] = []
        
        return backup_data
        
    except Exception as e:
        print(f"❌ 数据备份失败: {e}")
        return {}


def recreate_tables():
    """重新创建所有表"""
    print("🔧 重新创建数据库表...")
    
    try:
        # 删除所有表
        print("  🗑️ 删除旧表...")
        Base.metadata.drop_all(bind=engine)
        
        # 创建所有表
        print("  🏗️ 创建新表...")
        Base.metadata.create_all(bind=engine)
        
        # 验证表创建
        inspector = inspect(engine)
        created_tables = set(inspector.get_table_names())
        expected_tables = {'users', 'sms_codes', 'provinces', 'cities', 'user_tokens'}
        
        if expected_tables.issubset(created_tables):
            print("  ✅ 所有表创建成功")
            return True
        else:
            missing = expected_tables - created_tables
            print(f"  ❌ 缺少表: {missing}")
            return False
            
    except Exception as e:
        print(f"❌ 重新创建表失败: {e}")
        return False


def restore_data(backup_data):
    """恢复数据"""
    print("📥 恢复数据...")
    
    if not backup_data:
        print("  ⚠️ 没有备份数据可恢复")
        return True
    
    try:
        with engine.connect() as conn:
            trans = conn.begin()
            
            try:
                # 恢复省份数据
                if backup_data.get('provinces'):
                    print(f"  📍 恢复省份数据: {len(backup_data['provinces'])} 条")
                    for province in backup_data['provinces']:
                        conn.execute(text("""
                            INSERT INTO provinces (uuid, code, name, is_active, is_deleted, created_at, updated_at)
                            VALUES (:uuid, :code, :name, :is_active, :is_deleted, :created_at, :updated_at)
                            ON CONFLICT (uuid) DO NOTHING
                        """), province)
                
                # 恢复城市数据
                if backup_data.get('cities'):
                    print(f"  🏙️ 恢复城市数据: {len(backup_data['cities'])} 条")
                    for city in backup_data['cities']:
                        conn.execute(text("""
                            INSERT INTO cities (uuid, code, name, province_code, is_active, is_deleted, created_at, updated_at)
                            VALUES (:uuid, :code, :name, :province_code, :is_active, :is_deleted, :created_at, :updated_at)
                            ON CONFLICT (uuid) DO NOTHING
                        """), city)
                
                # 恢复用户数据
                if backup_data.get('users'):
                    print(f"  👤 恢复用户数据: {len(backup_data['users'])} 条")
                    for user in backup_data['users']:
                        conn.execute(text("""
                            INSERT INTO users (uuid, phone, username, avatar_url, province_code, city_code, address,
                                             is_active, is_deleted, created_at, updated_at, last_login)
                            VALUES (:uuid, :phone, :username, :avatar_url, :province_code, :city_code, :address,
                                   :is_active, :is_deleted, :created_at, :updated_at, :last_login)
                            ON CONFLICT (uuid) DO NOTHING
                        """), user)
                
                trans.commit()
                print("  ✅ 数据恢复成功")
                return True
                
            except Exception as e:
                trans.rollback()
                print(f"  ❌ 数据恢复失败: {e}")
                return False
                
    except Exception as e:
        print(f"❌ 数据恢复失败: {e}")
        return False


def init_basic_data():
    """初始化基础数据"""
    print("🌍 初始化基础数据...")
    
    try:
        # 检查是否已有省市数据
        with engine.connect() as conn:
            province_count = conn.execute(text("SELECT COUNT(*) FROM provinces WHERE is_deleted = false")).scalar()
            city_count = conn.execute(text("SELECT COUNT(*) FROM cities WHERE is_deleted = false")).scalar()
            
            if province_count > 0 and city_count > 0:
                print(f"  ✅ 已有省市数据 (省份: {province_count}, 城市: {city_count})")
                return True
        
        # 运行省市数据初始化
        from scripts.smart_init import init_provinces_cities
        return init_provinces_cities()
        
    except Exception as e:
        print(f"❌ 初始化基础数据失败: {e}")
        return False


def verify_database():
    """验证数据库"""
    print("🔍 验证数据库结构...")
    
    try:
        from scripts.check_database_structure import check_table_exists, check_table_columns
        
        # 检查表存在性
        tables_ok = check_table_exists()
        if not tables_ok:
            return False
        
        # 检查字段结构
        columns_ok = check_table_columns()
        if not columns_ok:
            return False
        
        print("✅ 数据库结构验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据库验证失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始数据库修复...")
    print("=" * 60)
    
    # 测试数据库连接
    try:
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
            print("✅ 数据库连接成功")
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False
    
    # 询问用户确认
    print("\n⚠️ 警告: 此操作将重建数据库表结构")
    print("📋 修复步骤:")
    print("1. 备份现有数据")
    print("2. 删除所有表")
    print("3. 重新创建表结构")
    print("4. 恢复备份数据")
    print("5. 初始化基础数据")
    print("6. 验证数据库结构")
    
    confirm = input("\n是否继续? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("❌ 用户取消操作")
        return False
    
    # 执行修复步骤
    steps = [
        ("备份数据", lambda: backup_data()),
        ("重新创建表", recreate_tables),
        ("初始化基础数据", init_basic_data),
        ("验证数据库", verify_database)
    ]
    
    backup_data_result = None
    
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        
        try:
            if step_name == "备份数据":
                backup_data_result = step_func()
                success = True  # 备份失败不影响继续
            elif step_name == "恢复数据":
                success = step_func(backup_data_result)
            else:
                success = step_func()
            
            if success:
                print(f"✅ {step_name} 完成")
            else:
                print(f"❌ {step_name} 失败")
                if step_name in ["重新创建表", "验证数据库"]:
                    return False
        except Exception as e:
            print(f"❌ {step_name} 执行失败: {e}")
            if step_name in ["重新创建表", "验证数据库"]:
                return False
    
    # 如果有备份数据，尝试恢复
    if backup_data_result:
        print(f"\n{'='*20} 恢复数据 {'='*20}")
        restore_success = restore_data(backup_data_result)
        if restore_success:
            print("✅ 数据恢复完成")
        else:
            print("⚠️ 数据恢复失败，但基础结构已修复")
    
    print("\n" + "=" * 60)
    print("🎉 数据库修复完成！")
    print("=" * 60)
    
    print("\n💡 后续建议:")
    print("1. 运行: python3 scripts/check_database_structure.py")
    print("2. 重新启动应用: python3 run.py")
    print("3. 测试基本功能")
    
    return True


if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ 数据库修复失败！")
        print("💡 可以尝试:")
        print("1. 检查数据库连接配置")
        print("2. 确保数据库服务正在运行")
        print("3. 检查数据库权限")
        sys.exit(1)
