#!/usr/bin/env python3
"""
创建AI对话会话表的脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from app.config import settings
from app.models.conversation import AIConversation
from app.database import Base
import structlog

logger = structlog.get_logger("create_conversation_table")


def create_conversation_table():
    """创建AI对话会话表"""
    try:
        # 创建数据库引擎
        engine = create_engine(settings.database_url)
        
        # 创建表
        AIConversation.__table__.create(engine, checkfirst=True)
        
        logger.info("AI对话会话表创建成功")
        
        # 验证表是否创建成功
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'ai_conversations'
            """))
            
            if result.fetchone():
                logger.info("表创建验证成功")
                
                # 显示表结构
                result = conn.execute(text("""
                    SELECT column_name, data_type, is_nullable, column_default
                    FROM information_schema.columns 
                    WHERE table_schema = 'public' 
                    AND table_name = 'ai_conversations'
                    ORDER BY ordinal_position
                """))
                
                print("\n=== AI对话会话表结构 ===")
                for row in result:
                    print(f"字段: {row[0]}, 类型: {row[1]}, 可空: {row[2]}, 默认值: {row[3]}")
                    
            else:
                logger.error("表创建验证失败")
                return False
                
        return True
        
    except Exception as e:
        logger.error("创建AI对话会话表失败", error=str(e))
        return False


def check_foreign_key():
    """检查外键约束"""
    try:
        engine = create_engine(settings.database_url)
        
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT 
                    tc.constraint_name,
                    tc.table_name,
                    kcu.column_name,
                    ccu.table_name AS foreign_table_name,
                    ccu.column_name AS foreign_column_name
                FROM information_schema.table_constraints AS tc
                JOIN information_schema.key_column_usage AS kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                JOIN information_schema.constraint_column_usage AS ccu
                    ON ccu.constraint_name = tc.constraint_name
                    AND ccu.table_schema = tc.table_schema
                WHERE tc.constraint_type = 'FOREIGN KEY'
                    AND tc.table_name = 'ai_conversations'
            """))
            
            print("\n=== 外键约束 ===")
            for row in result:
                print(f"约束名: {row[0]}")
                print(f"表: {row[1]}.{row[2]} -> {row[3]}.{row[4]}")
                
    except Exception as e:
        logger.error("检查外键约束失败", error=str(e))


def main():
    """主函数"""
    print("开始创建AI对话会话表...")
    
    if create_conversation_table():
        print("✅ AI对话会话表创建成功！")
        check_foreign_key()
    else:
        print("❌ AI对话会话表创建失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
