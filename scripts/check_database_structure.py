#!/usr/bin/env python3
"""
数据库结构检查脚本
详细检查数据库表结构、索引、约束等
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import text, inspect
from sqlalchemy.orm import sessionmaker
from app.database import engine
from app.models.user import User, SMSCode, Province, City, UserToken, Base
import structlog

logger = structlog.get_logger("db_check")


def check_table_exists():
    """检查所有必需的表是否存在"""
    print("🔍 检查数据库表...")
    
    try:
        inspector = inspect(engine)
        existing_tables = set(inspector.get_table_names())
        
        # 期望的表
        expected_tables = {
            'users': '用户表',
            'sms_codes': '短信验证码表', 
            'provinces': '省份表',
            'cities': '城市表',
            'user_tokens': '用户令牌表'
        }
        
        print(f"📊 数据库中现有表: {len(existing_tables)} 个")
        for table in sorted(existing_tables):
            print(f"  - {table}")
        
        print(f"\n📋 期望的表: {len(expected_tables)} 个")
        
        missing_tables = []
        for table_name, description in expected_tables.items():
            if table_name in existing_tables:
                print(f"  ✅ {table_name} ({description})")
            else:
                print(f"  ❌ {table_name} ({description}) - 缺失")
                missing_tables.append(table_name)
        
        if missing_tables:
            print(f"\n❌ 缺少 {len(missing_tables)} 个表: {missing_tables}")
            return False
        else:
            print(f"\n✅ 所有 {len(expected_tables)} 个表都存在")
            return True
            
    except Exception as e:
        print(f"❌ 检查表失败: {e}")
        return False


def check_table_columns():
    """检查表字段结构"""
    print("\n🔍 检查表字段结构...")
    
    try:
        inspector = inspect(engine)
        
        # 期望的表结构
        expected_structure = {
            'users': {
                'required_columns': [
                    'id', 'uuid', 'phone', 'username', 'avatar_url', 
                    'province_code', 'city_code', 'address', 'is_active', 
                    'is_deleted', 'created_at', 'updated_at', 'deleted_at', 'last_login'
                ],
                'description': '用户表'
            },
            'sms_codes': {
                'required_columns': [
                    'id', 'uuid', 'phone', 'code', 'purpose', 'is_used', 
                    'is_deleted', 'expires_at', 'created_at', 'updated_at', 'deleted_at'
                ],
                'description': '短信验证码表'
            },
            'provinces': {
                'required_columns': [
                    'id', 'uuid', 'code', 'name', 'is_active', 'is_deleted',
                    'created_at', 'updated_at', 'deleted_at'
                ],
                'description': '省份表'
            },
            'cities': {
                'required_columns': [
                    'id', 'uuid', 'code', 'name', 'province_code', 'is_active', 
                    'is_deleted', 'created_at', 'updated_at', 'deleted_at'
                ],
                'description': '城市表'
            },
            'user_tokens': {
                'required_columns': [
                    'id', 'uuid', 'user_id', 'access_token_jti', 'refresh_token_jti',
                    'device_id', 'device_name', 'client_ip', 'user_agent', 'is_active',
                    'is_deleted', 'access_token_expires_at', 'refresh_token_expires_at',
                    'last_used_at', 'created_at', 'updated_at', 'deleted_at'
                ],
                'description': '用户令牌表'
            }
        }
        
        all_columns_ok = True
        
        for table_name, table_info in expected_structure.items():
            print(f"\n📋 检查 {table_name} ({table_info['description']}):")
            
            try:
                existing_columns = [col['name'] for col in inspector.get_columns(table_name)]
                required_columns = table_info['required_columns']
                
                missing_columns = set(required_columns) - set(existing_columns)
                extra_columns = set(existing_columns) - set(required_columns)
                
                if missing_columns:
                    print(f"  ❌ 缺少字段: {missing_columns}")
                    all_columns_ok = False
                
                if extra_columns:
                    print(f"  ⚠️ 额外字段: {extra_columns}")
                
                if not missing_columns and not extra_columns:
                    print(f"  ✅ 字段结构完整 ({len(required_columns)} 个字段)")
                else:
                    print(f"  📊 期望字段: {len(required_columns)} 个")
                    print(f"  📊 实际字段: {len(existing_columns)} 个")
                    
            except Exception as e:
                print(f"  ❌ 检查失败: {e}")
                all_columns_ok = False
        
        return all_columns_ok
        
    except Exception as e:
        print(f"❌ 检查字段结构失败: {e}")
        return False


def check_indexes():
    """检查索引"""
    print("\n🔍 检查数据库索引...")
    
    try:
        inspector = inspect(engine)
        
        # 期望的索引
        expected_indexes = {
            'users': ['uuid', 'phone', 'is_active', 'is_deleted'],
            'sms_codes': ['uuid', 'phone', 'purpose', 'expires_at'],
            'provinces': ['uuid', 'code', 'is_active'],
            'cities': ['uuid', 'code', 'province_code', 'is_active'],
            'user_tokens': ['uuid', 'user_id', 'access_token_jti', 'refresh_token_jti', 'is_active']
        }
        
        for table_name, expected_index_columns in expected_indexes.items():
            print(f"\n📋 检查 {table_name} 表索引:")
            
            try:
                indexes = inspector.get_indexes(table_name)
                index_columns = set()
                
                for index in indexes:
                    for column in index['column_names']:
                        index_columns.add(column)
                
                missing_indexes = set(expected_index_columns) - index_columns
                
                if missing_indexes:
                    print(f"  ⚠️ 可能缺少索引的字段: {missing_indexes}")
                else:
                    print(f"  ✅ 关键字段都有索引")
                    
                print(f"  📊 总索引数: {len(indexes)}")
                
            except Exception as e:
                print(f"  ❌ 检查索引失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查索引失败: {e}")
        return False


def check_foreign_keys():
    """检查外键约束"""
    print("\n🔍 检查外键约束...")
    
    try:
        inspector = inspect(engine)
        
        # 期望的外键
        expected_foreign_keys = {
            'cities': [('province_code', 'provinces', 'code')],
            'user_tokens': [('user_id', 'users', 'id')]
        }
        
        for table_name, expected_fks in expected_foreign_keys.items():
            print(f"\n📋 检查 {table_name} 表外键:")
            
            try:
                foreign_keys = inspector.get_foreign_keys(table_name)
                
                if not foreign_keys and expected_fks:
                    print(f"  ⚠️ 未找到外键约束，但期望有 {len(expected_fks)} 个")
                elif foreign_keys:
                    print(f"  ✅ 找到 {len(foreign_keys)} 个外键约束")
                    for fk in foreign_keys:
                        print(f"    - {fk['constrained_columns']} -> {fk['referred_table']}.{fk['referred_columns']}")
                else:
                    print(f"  ✅ 无外键约束（符合预期）")
                    
            except Exception as e:
                print(f"  ❌ 检查外键失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查外键失败: {e}")
        return False


def check_data_counts():
    """检查数据量"""
    print("\n🔍 检查数据量...")
    
    try:
        session_local = sessionmaker(bind=engine)
        session = session_local()
        
        try:
            # 检查各表数据量
            tables_to_check = [
                (User, '用户'),
                (SMSCode, '短信验证码'),
                (Province, '省份'),
                (City, '城市'),
                (UserToken, '用户令牌')
            ]
            
            for model, description in tables_to_check:
                try:
                    count = session.query(model).count()
                    active_count = session.query(model).filter(
                        getattr(model, 'is_deleted', True) == False
                    ).count() if hasattr(model, 'is_deleted') else count
                    
                    print(f"  📊 {description}: 总计 {count} 条，活跃 {active_count} 条")
                    
                except Exception as e:
                    print(f"  ❌ 检查 {description} 数据失败: {e}")
            
            return True
            
        finally:
            session.close()
            
    except Exception as e:
        print(f"❌ 检查数据量失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始数据库结构检查...")
    print("=" * 60)
    
    # 测试数据库连接
    try:
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
            print("✅ 数据库连接成功")
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False
    
    # 执行各项检查
    checks = [
        ("表存在性检查", check_table_exists),
        ("表字段结构检查", check_table_columns),
        ("索引检查", check_indexes),
        ("外键约束检查", check_foreign_keys),
        ("数据量检查", check_data_counts)
    ]
    
    results = []
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name} 执行失败: {e}")
            results.append((check_name, False))
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 检查结果汇总")
    print("=" * 60)
    
    passed = 0
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{check_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项检查通过")
    
    if passed == len(results):
        print("🎉 数据库结构完全正确！")
        return True
    else:
        print("⚠️ 数据库结构存在问题，建议运行修复脚本")
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💡 修复建议:")
        print("1. 运行: python3 scripts/smart_init.py")
        print("2. 或运行: python3 scripts/create_token_table.py")
        print("3. 或运行: python3 run.py --init-db")
        sys.exit(1)
