#!/usr/bin/env python3
"""
省市数据管理脚本
提供省市数据的查看、更新、验证等功能
"""

import sys
import argparse
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.orm import sessionmaker
from sqlalchemy import func
from app.database import engine
from app.models.user import Province, City
import structlog

logger = structlog.get_logger("provinces_cities_manager")


def show_statistics():
    """显示省市数据统计"""
    print("📊 省市数据统计")
    print("=" * 50)
    
    try:
        session_local = sessionmaker(bind=engine)
        session = session_local()
        
        try:
            # 省份统计
            total_provinces = session.query(Province).count()
            active_provinces = session.query(Province).filter(Province.is_deleted == False).count()
            
            print(f"📍 省份数据:")
            print(f"  总数: {total_provinces}")
            print(f"  活跃: {active_provinces}")
            print(f"  已删除: {total_provinces - active_provinces}")
            
            # 城市统计
            total_cities = session.query(City).count()
            active_cities = session.query(City).filter(City.is_deleted == False).count()
            
            print(f"\n🏙️ 城市数据:")
            print(f"  总数: {total_cities}")
            print(f"  活跃: {active_cities}")
            print(f"  已删除: {total_cities - active_cities}")
            
            # 按省份统计城市
            print(f"\n📋 各省份城市数量 (前10名):")
            province_city_counts = session.query(
                Province.name,
                func.count(City.id).label('city_count')
            ).join(
                City, Province.code == City.province_code
            ).filter(
                Province.is_deleted == False,
                City.is_deleted == False
            ).group_by(
                Province.name
            ).order_by(
                func.count(City.id).desc()
            ).limit(10).all()
            
            for province_name, city_count in province_city_counts:
                print(f"  {province_name}: {city_count} 个城市")
            
            return True
            
        finally:
            session.close()
            
    except Exception as e:
        print(f"❌ 统计失败: {e}")
        return False


def list_provinces():
    """列出所有省份"""
    print("📍 省份列表")
    print("=" * 50)
    
    try:
        session_local = sessionmaker(bind=engine)
        session = session_local()
        
        try:
            provinces = session.query(Province).filter(
                Province.is_deleted == False
            ).order_by(Province.code).all()
            
            print(f"{'代码':<6} {'名称':<20} {'状态':<8}")
            print("-" * 40)
            
            for province in provinces:
                status = "活跃" if province.is_active else "禁用"
                print(f"{province.code:<6} {province.name:<20} {status:<8}")
            
            print(f"\n总计: {len(provinces)} 个省份")
            return True
            
        finally:
            session.close()
            
    except Exception as e:
        print(f"❌ 列出省份失败: {e}")
        return False


def list_cities(province_code=None):
    """列出城市"""
    if province_code:
        print(f"🏙️ {province_code} 省份的城市列表")
    else:
        print("🏙️ 城市列表")
    print("=" * 60)
    
    try:
        session_local = sessionmaker(bind=engine)
        session = session_local()
        
        try:
            query = session.query(City).filter(City.is_deleted == False)
            
            if province_code:
                query = query.filter(City.province_code == province_code)
                
            cities = query.order_by(City.code).all()
            
            print(f"{'代码':<8} {'名称':<25} {'省份代码':<8} {'状态':<8}")
            print("-" * 55)
            
            for city in cities:
                status = "活跃" if city.is_active else "禁用"
                print(f"{city.code:<8} {city.name:<25} {city.province_code:<8} {status:<8}")
            
            print(f"\n总计: {len(cities)} 个城市")
            return True
            
        finally:
            session.close()
            
    except Exception as e:
        print(f"❌ 列出城市失败: {e}")
        return False


def verify_data():
    """验证数据完整性"""
    print("🔍 验证省市数据完整性")
    print("=" * 50)
    
    try:
        session_local = sessionmaker(bind=engine)
        session = session_local()
        
        try:
            issues = []
            
            # 检查孤立城市
            orphan_cities = session.query(City).filter(
                ~City.province_code.in_(
                    session.query(Province.code).filter(Province.is_deleted == False)
                ),
                City.is_deleted == False
            ).all()
            
            if orphan_cities:
                issues.append(f"发现 {len(orphan_cities)} 个孤立城市（没有对应省份）")
                for city in orphan_cities[:5]:  # 只显示前5个
                    issues.append(f"  - {city.name} (代码: {city.code}, 省份代码: {city.province_code})")
                if len(orphan_cities) > 5:
                    issues.append(f"  ... 还有 {len(orphan_cities) - 5} 个")
            
            # 检查重复的省份代码
            duplicate_provinces = session.query(Province.code).filter(
                Province.is_deleted == False
            ).group_by(Province.code).having(func.count(Province.code) > 1).all()
            
            if duplicate_provinces:
                issues.append(f"发现 {len(duplicate_provinces)} 个重复的省份代码")
                for code_tuple in duplicate_provinces:
                    issues.append(f"  - 省份代码: {code_tuple[0]}")
            
            # 检查重复的城市代码
            duplicate_cities = session.query(City.code).filter(
                City.is_deleted == False
            ).group_by(City.code).having(func.count(City.code) > 1).all()
            
            if duplicate_cities:
                issues.append(f"发现 {len(duplicate_cities)} 个重复的城市代码")
                for code_tuple in duplicate_cities[:5]:  # 只显示前5个
                    issues.append(f"  - 城市代码: {code_tuple[0]}")
                if len(duplicate_cities) > 5:
                    issues.append(f"  ... 还有 {len(duplicate_cities) - 5} 个")
            
            # 检查空省份（没有城市的省份）
            empty_provinces = session.query(Province).filter(
                ~Province.code.in_(
                    session.query(City.province_code).filter(City.is_deleted == False)
                ),
                Province.is_deleted == False
            ).all()
            
            if empty_provinces:
                issues.append(f"发现 {len(empty_provinces)} 个空省份（没有城市）")
                for province in empty_provinces[:5]:  # 只显示前5个
                    issues.append(f"  - {province.name} (代码: {province.code})")
                if len(empty_provinces) > 5:
                    issues.append(f"  ... 还有 {len(empty_provinces) - 5} 个")
            
            # 输出结果
            if issues:
                print("❌ 发现以下问题:")
                for issue in issues:
                    print(issue)
                return False
            else:
                print("✅ 数据完整性验证通过")
                print("✅ 所有城市都有对应的省份")
                print("✅ 没有重复的代码")
                print("✅ 所有省份都有对应的城市")
                return True
            
        finally:
            session.close()
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def update_data():
    """更新省市数据"""
    print("🔄 更新省市数据")
    print("=" * 50)
    
    try:
        from scripts.init_provinces_cities import main as init_full_data
        return init_full_data()
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        return False


def search_location(keyword):
    """搜索省市"""
    print(f"🔍 搜索包含 '{keyword}' 的省市")
    print("=" * 50)
    
    try:
        session_local = sessionmaker(bind=engine)
        session = session_local()
        
        try:
            # 搜索省份
            provinces = session.query(Province).filter(
                Province.name.contains(keyword),
                Province.is_deleted == False
            ).all()
            
            if provinces:
                print("📍 匹配的省份:")
                for province in provinces:
                    print(f"  {province.code} - {province.name}")
            
            # 搜索城市
            cities = session.query(City).filter(
                City.name.contains(keyword),
                City.is_deleted == False
            ).all()
            
            if cities:
                print("\n🏙️ 匹配的城市:")
                for city in cities:
                    print(f"  {city.code} - {city.name} ({city.province_code})")
            
            if not provinces and not cities:
                print("❌ 没有找到匹配的省市")
                return False
            
            print(f"\n总计: {len(provinces)} 个省份, {len(cities)} 个城市")
            return True
            
        finally:
            session.close()
            
    except Exception as e:
        print(f"❌ 搜索失败: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="省市数据管理工具")
    parser.add_argument("--stats", action="store_true", help="显示统计信息")
    parser.add_argument("--list-provinces", action="store_true", help="列出所有省份")
    parser.add_argument("--list-cities", metavar="PROVINCE_CODE", nargs="?", const="", help="列出城市（可指定省份代码）")
    parser.add_argument("--verify", action="store_true", help="验证数据完整性")
    parser.add_argument("--update", action="store_true", help="更新省市数据")
    parser.add_argument("--search", metavar="KEYWORD", help="搜索省市")
    
    args = parser.parse_args()
    
    if not any(vars(args).values()):
        # 默认显示统计信息
        show_statistics()
        return
    
    success = True
    
    if args.stats:
        success &= show_statistics()
    
    if args.list_provinces:
        success &= list_provinces()
    
    if args.list_cities is not None:
        province_code = args.list_cities if args.list_cities else None
        success &= list_cities(province_code)
    
    if args.verify:
        success &= verify_data()
    
    if args.update:
        success &= update_data()
    
    if args.search:
        success &= search_location(args.search)
    
    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()
