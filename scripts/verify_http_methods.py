#!/usr/bin/env python3
"""
验证 HTTP 方法脚本
检查所有 API 端点是否只使用 GET 和 POST 方法
"""

import sys
import re
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def check_http_methods():
    """检查所有 API 端点的 HTTP 方法"""
    print("🔍 检查 API 端点的 HTTP 方法...")
    
    # API 端点文件路径
    endpoints_dir = project_root / "app" / "api" / "v1" / "endpoints"
    
    # 允许的 HTTP 方法
    allowed_methods = {"get", "post"}
    
    # 不允许的 HTTP 方法
    forbidden_methods = {"put", "delete", "patch", "head", "options", "trace"}
    
    # 检查结果
    all_endpoints = []
    forbidden_found = []
    
    # 遍历所有端点文件
    for py_file in endpoints_dir.glob("*.py"):
        if py_file.name == "__init__.py":
            continue
        
        print(f"\n📄 检查文件: {py_file.name}")
        
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找所有路由装饰器
            pattern = r'@router\.(get|post|put|delete|patch|head|options|trace)\s*\(\s*["\']([^"\']+)["\']'
            matches = re.findall(pattern, content, re.IGNORECASE)
            
            for method, path in matches:
                method_lower = method.lower()
                endpoint_info = {
                    "file": py_file.name,
                    "method": method.upper(),
                    "path": path,
                    "line": None
                }
                
                # 查找行号
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if f"@router.{method}" in line and path in line:
                        endpoint_info["line"] = i
                        break
                
                all_endpoints.append(endpoint_info)
                
                if method_lower in allowed_methods:
                    print(f"  ✅ {method.upper()} {path} (行 {endpoint_info['line']})")
                elif method_lower in forbidden_methods:
                    print(f"  ❌ {method.upper()} {path} (行 {endpoint_info['line']}) - 不允许的方法")
                    forbidden_found.append(endpoint_info)
                else:
                    print(f"  ⚠️ {method.upper()} {path} (行 {endpoint_info['line']}) - 未知方法")
                    forbidden_found.append(endpoint_info)
        
        except Exception as e:
            print(f"  ❌ 读取文件失败: {e}")
    
    # 输出总结
    print(f"\n{'='*60}")
    print("📊 检查结果总结")
    print('='*60)
    
    print(f"📋 总共找到 {len(all_endpoints)} 个 API 端点")
    
    # 按方法分组统计
    method_counts = {}
    for endpoint in all_endpoints:
        method = endpoint["method"]
        method_counts[method] = method_counts.get(method, 0) + 1
    
    print(f"\n📈 方法统计:")
    for method, count in sorted(method_counts.items()):
        status = "✅" if method.lower() in allowed_methods else "❌"
        print(f"  {status} {method}: {count} 个")
    
    if forbidden_found:
        print(f"\n❌ 发现 {len(forbidden_found)} 个不符合要求的端点:")
        for endpoint in forbidden_found:
            print(f"  - {endpoint['file']}:{endpoint['line']} - {endpoint['method']} {endpoint['path']}")
        
        print(f"\n🔧 需要修改的建议:")
        for endpoint in forbidden_found:
            if endpoint['method'] in ['PUT', 'PATCH']:
                new_path = endpoint['path']
                if not new_path.startswith('/update-') and not new_path.startswith('/create-'):
                    # 为更新操作添加前缀
                    if '/' in new_path[1:]:
                        parts = new_path.split('/')
                        new_path = f"/update-{parts[-1]}"
                    else:
                        new_path = f"/update-{new_path[1:]}"
                
                print(f"  - 将 {endpoint['method']} {endpoint['path']} 改为 POST {new_path}")
            elif endpoint['method'] == 'DELETE':
                new_path = endpoint['path']
                if not new_path.startswith('/delete-'):
                    if '/' in new_path[1:]:
                        parts = new_path.split('/')
                        new_path = f"/delete-{parts[-1]}"
                    else:
                        new_path = f"/delete-{new_path[1:]}"
                
                print(f"  - 将 {endpoint['method']} {endpoint['path']} 改为 POST {new_path}")
        
        return False
    else:
        print(f"\n🎉 所有 API 端点都符合要求！")
        print(f"✅ 只使用了 GET 和 POST 方法")
        return True


def show_all_endpoints():
    """显示所有 API 端点"""
    print(f"\n📋 所有 API 端点列表:")
    print("-" * 60)
    
    endpoints_dir = project_root / "app" / "api" / "v1" / "endpoints"
    
    for py_file in endpoints_dir.glob("*.py"):
        if py_file.name == "__init__.py":
            continue
        
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找所有路由装饰器
            pattern = r'@router\.(get|post|put|delete|patch|head|options|trace)\s*\(\s*["\']([^"\']+)["\'].*?tags=\[([^\]]+)\]'
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            
            if matches:
                print(f"\n📄 {py_file.name}:")
                for method, path, tags in matches:
                    # 清理 tags
                    tags_clean = tags.replace('"', '').replace("'", "").strip()
                    print(f"  {method.upper()} /api/v1{path} - {tags_clean}")
        
        except Exception as e:
            print(f"  ❌ 读取文件失败: {e}")


def main():
    """主函数"""
    print("🚀 开始验证 HTTP 方法...")
    print("=" * 60)
    
    # 检查 HTTP 方法
    success = check_http_methods()
    
    # 显示所有端点
    show_all_endpoints()
    
    if success:
        print(f"\n🎉 验证通过！所有 API 端点都只使用 GET 和 POST 方法。")
        return True
    else:
        print(f"\n❌ 验证失败！请修改上述不符合要求的端点。")
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
