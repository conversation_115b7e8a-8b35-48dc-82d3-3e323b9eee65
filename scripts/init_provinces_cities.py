#!/usr/bin/env python3
"""
省市数据初始化脚本
获取最新的中国省市数据并存储到数据库中
"""

import sys
import json
import uuid
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.orm import sessionmaker
from app.database import engine
from app.models.user import Province, City
from app.utils.timezone import to_utc_for_db
import structlog

logger = structlog.get_logger("provinces_cities")


def get_china_provinces_cities_data():
    """
    获取中国省市数据
    使用国家统计局标准的行政区划代码
    """
    # 最新的省级行政区数据（2023年）
    provinces_data = [
        {"code": "11", "name": "北京市"},
        {"code": "12", "name": "天津市"},
        {"code": "13", "name": "河北省"},
        {"code": "14", "name": "山西省"},
        {"code": "15", "name": "内蒙古自治区"},
        {"code": "21", "name": "辽宁省"},
        {"code": "22", "name": "吉林省"},
        {"code": "23", "name": "黑龙江省"},
        {"code": "31", "name": "上海市"},
        {"code": "32", "name": "江苏省"},
        {"code": "33", "name": "浙江省"},
        {"code": "34", "name": "安徽省"},
        {"code": "35", "name": "福建省"},
        {"code": "36", "name": "江西省"},
        {"code": "37", "name": "山东省"},
        {"code": "41", "name": "河南省"},
        {"code": "42", "name": "湖北省"},
        {"code": "43", "name": "湖南省"},
        {"code": "44", "name": "广东省"},
        {"code": "45", "name": "广西壮族自治区"},
        {"code": "46", "name": "海南省"},
        {"code": "50", "name": "重庆市"},
        {"code": "51", "name": "四川省"},
        {"code": "52", "name": "贵州省"},
        {"code": "53", "name": "云南省"},
        {"code": "54", "name": "西藏自治区"},
        {"code": "61", "name": "陕西省"},
        {"code": "62", "name": "甘肃省"},
        {"code": "63", "name": "青海省"},
        {"code": "64", "name": "宁夏回族自治区"},
        {"code": "65", "name": "新疆维吾尔自治区"},
        {"code": "71", "name": "台湾省"},
        {"code": "81", "name": "香港特别行政区"},
        {"code": "82", "name": "澳门特别行政区"}
    ]
    
    # 主要城市数据（地级市）
    cities_data = [
        # 北京市
        {"code": "1101", "name": "北京市", "province_code": "11"},
        
        # 天津市
        {"code": "1201", "name": "天津市", "province_code": "12"},
        
        # 河北省
        {"code": "1301", "name": "石家庄市", "province_code": "13"},
        {"code": "1302", "name": "唐山市", "province_code": "13"},
        {"code": "1303", "name": "秦皇岛市", "province_code": "13"},
        {"code": "1304", "name": "邯郸市", "province_code": "13"},
        {"code": "1305", "name": "邢台市", "province_code": "13"},
        {"code": "1306", "name": "保定市", "province_code": "13"},
        {"code": "1307", "name": "张家口市", "province_code": "13"},
        {"code": "1308", "name": "承德市", "province_code": "13"},
        {"code": "1309", "name": "沧州市", "province_code": "13"},
        {"code": "1310", "name": "廊坊市", "province_code": "13"},
        {"code": "1311", "name": "衡水市", "province_code": "13"},
        
        # 山西省
        {"code": "1401", "name": "太原市", "province_code": "14"},
        {"code": "1402", "name": "大同市", "province_code": "14"},
        {"code": "1403", "name": "阳泉市", "province_code": "14"},
        {"code": "1404", "name": "长治市", "province_code": "14"},
        {"code": "1405", "name": "晋城市", "province_code": "14"},
        {"code": "1406", "name": "朔州市", "province_code": "14"},
        {"code": "1407", "name": "晋中市", "province_code": "14"},
        {"code": "1408", "name": "运城市", "province_code": "14"},
        {"code": "1409", "name": "忻州市", "province_code": "14"},
        {"code": "1410", "name": "临汾市", "province_code": "14"},
        {"code": "1411", "name": "吕梁市", "province_code": "14"},
        
        # 内蒙古自治区
        {"code": "1501", "name": "呼和浩特市", "province_code": "15"},
        {"code": "1502", "name": "包头市", "province_code": "15"},
        {"code": "1503", "name": "乌海市", "province_code": "15"},
        {"code": "1504", "name": "赤峰市", "province_code": "15"},
        {"code": "1505", "name": "通辽市", "province_code": "15"},
        {"code": "1506", "name": "鄂尔多斯市", "province_code": "15"},
        {"code": "1507", "name": "呼伦贝尔市", "province_code": "15"},
        {"code": "1508", "name": "巴彦淖尔市", "province_code": "15"},
        {"code": "1509", "name": "乌兰察布市", "province_code": "15"},
        {"code": "1522", "name": "兴安盟", "province_code": "15"},
        {"code": "1525", "name": "锡林郭勒盟", "province_code": "15"},
        {"code": "1529", "name": "阿拉善盟", "province_code": "15"},
        
        # 辽宁省
        {"code": "2101", "name": "沈阳市", "province_code": "21"},
        {"code": "2102", "name": "大连市", "province_code": "21"},
        {"code": "2103", "name": "鞍山市", "province_code": "21"},
        {"code": "2104", "name": "抚顺市", "province_code": "21"},
        {"code": "2105", "name": "本溪市", "province_code": "21"},
        {"code": "2106", "name": "丹东市", "province_code": "21"},
        {"code": "2107", "name": "锦州市", "province_code": "21"},
        {"code": "2108", "name": "营口市", "province_code": "21"},
        {"code": "2109", "name": "阜新市", "province_code": "21"},
        {"code": "2110", "name": "辽阳市", "province_code": "21"},
        {"code": "2111", "name": "盘锦市", "province_code": "21"},
        {"code": "2112", "name": "铁岭市", "province_code": "21"},
        {"code": "2113", "name": "朝阳市", "province_code": "21"},
        {"code": "2114", "name": "葫芦岛市", "province_code": "21"},
        
        # 吉林省
        {"code": "2201", "name": "长春市", "province_code": "22"},
        {"code": "2202", "name": "吉林市", "province_code": "22"},
        {"code": "2203", "name": "四平市", "province_code": "22"},
        {"code": "2204", "name": "辽源市", "province_code": "22"},
        {"code": "2205", "name": "通化市", "province_code": "22"},
        {"code": "2206", "name": "白山市", "province_code": "22"},
        {"code": "2207", "name": "松原市", "province_code": "22"},
        {"code": "2208", "name": "白城市", "province_code": "22"},
        {"code": "2224", "name": "延边朝鲜族自治州", "province_code": "22"},
        
        # 黑龙江省
        {"code": "2301", "name": "哈尔滨市", "province_code": "23"},
        {"code": "2302", "name": "齐齐哈尔市", "province_code": "23"},
        {"code": "2303", "name": "鸡西市", "province_code": "23"},
        {"code": "2304", "name": "鹤岗市", "province_code": "23"},
        {"code": "2305", "name": "双鸭山市", "province_code": "23"},
        {"code": "2306", "name": "大庆市", "province_code": "23"},
        {"code": "2307", "name": "伊春市", "province_code": "23"},
        {"code": "2308", "name": "佳木斯市", "province_code": "23"},
        {"code": "2309", "name": "七台河市", "province_code": "23"},
        {"code": "2310", "name": "牡丹江市", "province_code": "23"},
        {"code": "2311", "name": "黑河市", "province_code": "23"},
        {"code": "2312", "name": "绥化市", "province_code": "23"},
        {"code": "2327", "name": "大兴安岭地区", "province_code": "23"},
        
        # 上海市
        {"code": "3101", "name": "上海市", "province_code": "31"},
        
        # 江苏省
        {"code": "3201", "name": "南京市", "province_code": "32"},
        {"code": "3202", "name": "无锡市", "province_code": "32"},
        {"code": "3203", "name": "徐州市", "province_code": "32"},
        {"code": "3204", "name": "常州市", "province_code": "32"},
        {"code": "3205", "name": "苏州市", "province_code": "32"},
        {"code": "3206", "name": "南通市", "province_code": "32"},
        {"code": "3207", "name": "连云港市", "province_code": "32"},
        {"code": "3208", "name": "淮安市", "province_code": "32"},
        {"code": "3209", "name": "盐城市", "province_code": "32"},
        {"code": "3210", "name": "扬州市", "province_code": "32"},
        {"code": "3211", "name": "镇江市", "province_code": "32"},
        {"code": "3212", "name": "泰州市", "province_code": "32"},
        {"code": "3213", "name": "宿迁市", "province_code": "32"},
        
        # 浙江省
        {"code": "3301", "name": "杭州市", "province_code": "33"},
        {"code": "3302", "name": "宁波市", "province_code": "33"},
        {"code": "3303", "name": "温州市", "province_code": "33"},
        {"code": "3304", "name": "嘉兴市", "province_code": "33"},
        {"code": "3305", "name": "湖州市", "province_code": "33"},
        {"code": "3306", "name": "绍兴市", "province_code": "33"},
        {"code": "3307", "name": "金华市", "province_code": "33"},
        {"code": "3308", "name": "衢州市", "province_code": "33"},
        {"code": "3309", "name": "舟山市", "province_code": "33"},
        {"code": "3310", "name": "台州市", "province_code": "33"},
        {"code": "3311", "name": "丽水市", "province_code": "33"},
        
        # 安徽省
        {"code": "3401", "name": "合肥市", "province_code": "34"},
        {"code": "3402", "name": "芜湖市", "province_code": "34"},
        {"code": "3403", "name": "蚌埠市", "province_code": "34"},
        {"code": "3404", "name": "淮南市", "province_code": "34"},
        {"code": "3405", "name": "马鞍山市", "province_code": "34"},
        {"code": "3406", "name": "淮北市", "province_code": "34"},
        {"code": "3407", "name": "铜陵市", "province_code": "34"},
        {"code": "3408", "name": "安庆市", "province_code": "34"},
        {"code": "3410", "name": "黄山市", "province_code": "34"},
        {"code": "3411", "name": "滁州市", "province_code": "34"},
        {"code": "3412", "name": "阜阳市", "province_code": "34"},
        {"code": "3413", "name": "宿州市", "province_code": "34"},
        {"code": "3415", "name": "六安市", "province_code": "34"},
        {"code": "3416", "name": "亳州市", "province_code": "34"},
        {"code": "3417", "name": "池州市", "province_code": "34"},
        {"code": "3418", "name": "宣城市", "province_code": "34"},
        
        # 福建省
        {"code": "3501", "name": "福州市", "province_code": "35"},
        {"code": "3502", "name": "厦门市", "province_code": "35"},
        {"code": "3503", "name": "莆田市", "province_code": "35"},
        {"code": "3504", "name": "三明市", "province_code": "35"},
        {"code": "3505", "name": "泉州市", "province_code": "35"},
        {"code": "3506", "name": "漳州市", "province_code": "35"},
        {"code": "3507", "name": "南平市", "province_code": "35"},
        {"code": "3508", "name": "龙岩市", "province_code": "35"},
        {"code": "3509", "name": "宁德市", "province_code": "35"},
        
        # 江西省
        {"code": "3601", "name": "南昌市", "province_code": "36"},
        {"code": "3602", "name": "景德镇市", "province_code": "36"},
        {"code": "3603", "name": "萍乡市", "province_code": "36"},
        {"code": "3604", "name": "九江市", "province_code": "36"},
        {"code": "3605", "name": "新余市", "province_code": "36"},
        {"code": "3606", "name": "鹰潭市", "province_code": "36"},
        {"code": "3607", "name": "赣州市", "province_code": "36"},
        {"code": "3608", "name": "吉安市", "province_code": "36"},
        {"code": "3609", "name": "宜春市", "province_code": "36"},
        {"code": "3610", "name": "抚州市", "province_code": "36"},
        {"code": "3611", "name": "上饶市", "province_code": "36"},
        
        # 山东省
        {"code": "3701", "name": "济南市", "province_code": "37"},
        {"code": "3702", "name": "青岛市", "province_code": "37"},
        {"code": "3703", "name": "淄博市", "province_code": "37"},
        {"code": "3704", "name": "枣庄市", "province_code": "37"},
        {"code": "3705", "name": "东营市", "province_code": "37"},
        {"code": "3706", "name": "烟台市", "province_code": "37"},
        {"code": "3707", "name": "潍坊市", "province_code": "37"},
        {"code": "3708", "name": "济宁市", "province_code": "37"},
        {"code": "3709", "name": "泰安市", "province_code": "37"},
        {"code": "3710", "name": "威海市", "province_code": "37"},
        {"code": "3711", "name": "日照市", "province_code": "37"},
        {"code": "3713", "name": "临沂市", "province_code": "37"},
        {"code": "3714", "name": "德州市", "province_code": "37"},
        {"code": "3715", "name": "聊城市", "province_code": "37"},
        {"code": "3716", "name": "滨州市", "province_code": "37"},
        {"code": "3717", "name": "菏泽市", "province_code": "37"},

        # 河南省
        {"code": "4101", "name": "郑州市", "province_code": "41"},
        {"code": "4102", "name": "开封市", "province_code": "41"},
        {"code": "4103", "name": "洛阳市", "province_code": "41"},
        {"code": "4104", "name": "平顶山市", "province_code": "41"},
        {"code": "4105", "name": "安阳市", "province_code": "41"},
        {"code": "4106", "name": "鹤壁市", "province_code": "41"},
        {"code": "4107", "name": "新乡市", "province_code": "41"},
        {"code": "4108", "name": "焦作市", "province_code": "41"},
        {"code": "4109", "name": "濮阳市", "province_code": "41"},
        {"code": "4110", "name": "许昌市", "province_code": "41"},
        {"code": "4111", "name": "漯河市", "province_code": "41"},
        {"code": "4112", "name": "三门峡市", "province_code": "41"},
        {"code": "4113", "name": "南阳市", "province_code": "41"},
        {"code": "4114", "name": "商丘市", "province_code": "41"},
        {"code": "4115", "name": "信阳市", "province_code": "41"},
        {"code": "4116", "name": "周口市", "province_code": "41"},
        {"code": "4117", "name": "驻马店市", "province_code": "41"},
        {"code": "4190", "name": "济源市", "province_code": "41"},

        # 湖北省
        {"code": "4201", "name": "武汉市", "province_code": "42"},
        {"code": "4202", "name": "黄石市", "province_code": "42"},
        {"code": "4203", "name": "十堰市", "province_code": "42"},
        {"code": "4205", "name": "宜昌市", "province_code": "42"},
        {"code": "4206", "name": "襄阳市", "province_code": "42"},
        {"code": "4207", "name": "鄂州市", "province_code": "42"},
        {"code": "4208", "name": "荆门市", "province_code": "42"},
        {"code": "4209", "name": "孝感市", "province_code": "42"},
        {"code": "4210", "name": "荆州市", "province_code": "42"},
        {"code": "4211", "name": "黄冈市", "province_code": "42"},
        {"code": "4212", "name": "咸宁市", "province_code": "42"},
        {"code": "4213", "name": "随州市", "province_code": "42"},
        {"code": "4228", "name": "恩施土家族苗族自治州", "province_code": "42"},
        {"code": "4290", "name": "仙桃市", "province_code": "42"},
        {"code": "4291", "name": "潜江市", "province_code": "42"},
        {"code": "4292", "name": "天门市", "province_code": "42"},
        {"code": "4293", "name": "神农架林区", "province_code": "42"},

        # 湖南省
        {"code": "4301", "name": "长沙市", "province_code": "43"},
        {"code": "4302", "name": "株洲市", "province_code": "43"},
        {"code": "4303", "name": "湘潭市", "province_code": "43"},
        {"code": "4304", "name": "衡阳市", "province_code": "43"},
        {"code": "4305", "name": "邵阳市", "province_code": "43"},
        {"code": "4306", "name": "岳阳市", "province_code": "43"},
        {"code": "4307", "name": "常德市", "province_code": "43"},
        {"code": "4308", "name": "张家界市", "province_code": "43"},
        {"code": "4309", "name": "益阳市", "province_code": "43"},
        {"code": "4310", "name": "郴州市", "province_code": "43"},
        {"code": "4311", "name": "永州市", "province_code": "43"},
        {"code": "4312", "name": "怀化市", "province_code": "43"},
        {"code": "4313", "name": "娄底市", "province_code": "43"},
        {"code": "4331", "name": "湘西土家族苗族自治州", "province_code": "43"},

        # 广东省
        {"code": "4401", "name": "广州市", "province_code": "44"},
        {"code": "4403", "name": "深圳市", "province_code": "44"},
        {"code": "4404", "name": "珠海市", "province_code": "44"},
        {"code": "4405", "name": "汕头市", "province_code": "44"},
        {"code": "4406", "name": "佛山市", "province_code": "44"},
        {"code": "4407", "name": "韶关市", "province_code": "44"},
        {"code": "4408", "name": "湛江市", "province_code": "44"},
        {"code": "4409", "name": "肇庆市", "province_code": "44"},
        {"code": "4412", "name": "江门市", "province_code": "44"},
        {"code": "4413", "name": "茂名市", "province_code": "44"},
        {"code": "4414", "name": "惠州市", "province_code": "44"},
        {"code": "4415", "name": "梅州市", "province_code": "44"},
        {"code": "4416", "name": "汕尾市", "province_code": "44"},
        {"code": "4417", "name": "河源市", "province_code": "44"},
        {"code": "4418", "name": "阳江市", "province_code": "44"},
        {"code": "4419", "name": "清远市", "province_code": "44"},
        {"code": "4420", "name": "东莞市", "province_code": "44"},
        {"code": "4451", "name": "中山市", "province_code": "44"},
        {"code": "4452", "name": "潮州市", "province_code": "44"},
        {"code": "4453", "name": "揭阳市", "province_code": "44"},
        {"code": "4454", "name": "云浮市", "province_code": "44"},

        # 广西壮族自治区
        {"code": "4501", "name": "南宁市", "province_code": "45"},
        {"code": "4502", "name": "柳州市", "province_code": "45"},
        {"code": "4503", "name": "桂林市", "province_code": "45"},
        {"code": "4504", "name": "梧州市", "province_code": "45"},
        {"code": "4505", "name": "北海市", "province_code": "45"},
        {"code": "4506", "name": "防城港市", "province_code": "45"},
        {"code": "4507", "name": "钦州市", "province_code": "45"},
        {"code": "4508", "name": "贵港市", "province_code": "45"},
        {"code": "4509", "name": "玉林市", "province_code": "45"},
        {"code": "4510", "name": "百色市", "province_code": "45"},
        {"code": "4511", "name": "贺州市", "province_code": "45"},
        {"code": "4512", "name": "河池市", "province_code": "45"},
        {"code": "4513", "name": "来宾市", "province_code": "45"},
        {"code": "4514", "name": "崇左市", "province_code": "45"},

        # 海南省
        {"code": "4601", "name": "海口市", "province_code": "46"},
        {"code": "4602", "name": "三亚市", "province_code": "46"},
        {"code": "4603", "name": "三沙市", "province_code": "46"},
        {"code": "4604", "name": "儋州市", "province_code": "46"},
        {"code": "4690", "name": "五指山市", "province_code": "46"},
        {"code": "4691", "name": "琼海市", "province_code": "46"},
        {"code": "4692", "name": "文昌市", "province_code": "46"},
        {"code": "4693", "name": "万宁市", "province_code": "46"},
        {"code": "4694", "name": "东方市", "province_code": "46"},
        {"code": "4695", "name": "定安县", "province_code": "46"},
        {"code": "4696", "name": "屯昌县", "province_code": "46"},
        {"code": "4697", "name": "澄迈县", "province_code": "46"},
        {"code": "4698", "name": "临高县", "province_code": "46"},
        {"code": "4699", "name": "白沙黎族自治县", "province_code": "46"},

        # 重庆市
        {"code": "5001", "name": "重庆市", "province_code": "50"},

        # 四川省
        {"code": "5101", "name": "成都市", "province_code": "51"},
        {"code": "5103", "name": "自贡市", "province_code": "51"},
        {"code": "5104", "name": "攀枝花市", "province_code": "51"},
        {"code": "5105", "name": "泸州市", "province_code": "51"},
        {"code": "5106", "name": "德阳市", "province_code": "51"},
        {"code": "5107", "name": "绵阳市", "province_code": "51"},
        {"code": "5108", "name": "广元市", "province_code": "51"},
        {"code": "5109", "name": "遂宁市", "province_code": "51"},
        {"code": "5110", "name": "内江市", "province_code": "51"},
        {"code": "5111", "name": "乐山市", "province_code": "51"},
        {"code": "5113", "name": "南充市", "province_code": "51"},
        {"code": "5114", "name": "眉山市", "province_code": "51"},
        {"code": "5115", "name": "宜宾市", "province_code": "51"},
        {"code": "5116", "name": "广安市", "province_code": "51"},
        {"code": "5117", "name": "达州市", "province_code": "51"},
        {"code": "5118", "name": "雅安市", "province_code": "51"},
        {"code": "5119", "name": "巴中市", "province_code": "51"},
        {"code": "5120", "name": "资阳市", "province_code": "51"},
        {"code": "5132", "name": "阿坝藏族羌族自治州", "province_code": "51"},
        {"code": "5133", "name": "甘孜藏族自治州", "province_code": "51"},
        {"code": "5134", "name": "凉山彝族自治州", "province_code": "51"},

        # 贵州省
        {"code": "5201", "name": "贵阳市", "province_code": "52"},
        {"code": "5202", "name": "六盘水市", "province_code": "52"},
        {"code": "5203", "name": "遵义市", "province_code": "52"},
        {"code": "5204", "name": "安顺市", "province_code": "52"},
        {"code": "5205", "name": "毕节市", "province_code": "52"},
        {"code": "5206", "name": "铜仁市", "province_code": "52"},
        {"code": "5223", "name": "黔西南布依族苗族自治州", "province_code": "52"},
        {"code": "5226", "name": "黔东南苗族侗族自治州", "province_code": "52"},
        {"code": "5227", "name": "黔南布依族苗族自治州", "province_code": "52"},

        # 云南省
        {"code": "5301", "name": "昆明市", "province_code": "53"},
        {"code": "5303", "name": "曲靖市", "province_code": "53"},
        {"code": "5304", "name": "玉溪市", "province_code": "53"},
        {"code": "5305", "name": "保山市", "province_code": "53"},
        {"code": "5306", "name": "昭通市", "province_code": "53"},
        {"code": "5307", "name": "丽江市", "province_code": "53"},
        {"code": "5308", "name": "普洱市", "province_code": "53"},
        {"code": "5309", "name": "临沧市", "province_code": "53"},
        {"code": "5323", "name": "楚雄彝族自治州", "province_code": "53"},
        {"code": "5325", "name": "红河哈尼族彝族自治州", "province_code": "53"},
        {"code": "5326", "name": "文山壮族苗族自治州", "province_code": "53"},
        {"code": "5328", "name": "西双版纳傣族自治州", "province_code": "53"},
        {"code": "5329", "name": "大理白族自治州", "province_code": "53"},
        {"code": "5331", "name": "德宏傣族景颇族自治州", "province_code": "53"},
        {"code": "5333", "name": "怒江傈僳族自治州", "province_code": "53"},
        {"code": "5334", "name": "迪庆藏族自治州", "province_code": "53"},

        # 西藏自治区
        {"code": "5401", "name": "拉萨市", "province_code": "54"},
        {"code": "5402", "name": "日喀则市", "province_code": "54"},
        {"code": "5403", "name": "昌都市", "province_code": "54"},
        {"code": "5404", "name": "林芝市", "province_code": "54"},
        {"code": "5405", "name": "山南市", "province_code": "54"},
        {"code": "5406", "name": "那曲市", "province_code": "54"},
        {"code": "5425", "name": "阿里地区", "province_code": "54"},

        # 陕西省
        {"code": "6101", "name": "西安市", "province_code": "61"},
        {"code": "6102", "name": "铜川市", "province_code": "61"},
        {"code": "6103", "name": "宝鸡市", "province_code": "61"},
        {"code": "6104", "name": "咸阳市", "province_code": "61"},
        {"code": "6105", "name": "渭南市", "province_code": "61"},
        {"code": "6106", "name": "延安市", "province_code": "61"},
        {"code": "6107", "name": "汉中市", "province_code": "61"},
        {"code": "6108", "name": "榆林市", "province_code": "61"},
        {"code": "6109", "name": "安康市", "province_code": "61"},
        {"code": "6110", "name": "商洛市", "province_code": "61"},

        # 甘肃省
        {"code": "6201", "name": "兰州市", "province_code": "62"},
        {"code": "6202", "name": "嘉峪关市", "province_code": "62"},
        {"code": "6203", "name": "金昌市", "province_code": "62"},
        {"code": "6204", "name": "白银市", "province_code": "62"},
        {"code": "6205", "name": "天水市", "province_code": "62"},
        {"code": "6206", "name": "武威市", "province_code": "62"},
        {"code": "6207", "name": "张掖市", "province_code": "62"},
        {"code": "6208", "name": "平凉市", "province_code": "62"},
        {"code": "6209", "name": "酒泉市", "province_code": "62"},
        {"code": "6210", "name": "庆阳市", "province_code": "62"},
        {"code": "6211", "name": "定西市", "province_code": "62"},
        {"code": "6212", "name": "陇南市", "province_code": "62"},
        {"code": "6229", "name": "临夏回族自治州", "province_code": "62"},
        {"code": "6230", "name": "甘南藏族自治州", "province_code": "62"},

        # 青海省
        {"code": "6301", "name": "西宁市", "province_code": "63"},
        {"code": "6302", "name": "海东市", "province_code": "63"},
        {"code": "6322", "name": "海北藏族自治州", "province_code": "63"},
        {"code": "6323", "name": "黄南藏族自治州", "province_code": "63"},
        {"code": "6325", "name": "海南藏族自治州", "province_code": "63"},
        {"code": "6326", "name": "果洛藏族自治州", "province_code": "63"},
        {"code": "6327", "name": "玉树藏族自治州", "province_code": "63"},
        {"code": "6328", "name": "海西蒙古族藏族自治州", "province_code": "63"},

        # 宁夏回族自治区
        {"code": "6401", "name": "银川市", "province_code": "64"},
        {"code": "6402", "name": "石嘴山市", "province_code": "64"},
        {"code": "6403", "name": "吴忠市", "province_code": "64"},
        {"code": "6404", "name": "固原市", "province_code": "64"},
        {"code": "6405", "name": "中卫市", "province_code": "64"},

        # 新疆维吾尔自治区
        {"code": "6501", "name": "乌鲁木齐市", "province_code": "65"},
        {"code": "6502", "name": "克拉玛依市", "province_code": "65"},
        {"code": "6504", "name": "吐鲁番市", "province_code": "65"},
        {"code": "6505", "name": "哈密市", "province_code": "65"},
        {"code": "6523", "name": "昌吉回族自治州", "province_code": "65"},
        {"code": "6527", "name": "博尔塔拉蒙古自治州", "province_code": "65"},
        {"code": "6528", "name": "巴音郭楞蒙古自治州", "province_code": "65"},
        {"code": "6529", "name": "阿克苏地区", "province_code": "65"},
        {"code": "6530", "name": "克孜勒苏柯尔克孜自治州", "province_code": "65"},
        {"code": "6531", "name": "喀什地区", "province_code": "65"},
        {"code": "6532", "name": "和田地区", "province_code": "65"},
        {"code": "6540", "name": "伊犁哈萨克自治州", "province_code": "65"},
        {"code": "6542", "name": "塔城地区", "province_code": "65"},
        {"code": "6543", "name": "阿勒泰地区", "province_code": "65"},
        {"code": "6590", "name": "石河子市", "province_code": "65"},
        {"code": "6591", "name": "阿拉尔市", "province_code": "65"},
        {"code": "6592", "name": "图木舒克市", "province_code": "65"},
        {"code": "6593", "name": "五家渠市", "province_code": "65"},
        {"code": "6594", "name": "北屯市", "province_code": "65"},
        {"code": "6595", "name": "铁门关市", "province_code": "65"},
        {"code": "6596", "name": "双河市", "province_code": "65"},
        {"code": "6597", "name": "可克达拉市", "province_code": "65"},
        {"code": "6598", "name": "昆玉市", "province_code": "65"},
        {"code": "6599", "name": "胡杨河市", "province_code": "65"},

        # 台湾省（基本城市）
        {"code": "7101", "name": "台北市", "province_code": "71"},
        {"code": "7102", "name": "高雄市", "province_code": "71"},
        {"code": "7103", "name": "台中市", "province_code": "71"},
        {"code": "7104", "name": "台南市", "province_code": "71"},
        {"code": "7105", "name": "新北市", "province_code": "71"},
        {"code": "7106", "name": "桃园市", "province_code": "71"},

        # 香港特别行政区
        {"code": "8101", "name": "香港", "province_code": "81"},

        # 澳门特别行政区
        {"code": "8201", "name": "澳门", "province_code": "82"}
    ]

    return provinces_data, cities_data


def clear_existing_data(session):
    """清除现有的省市数据"""
    print("🗑️ 清除现有省市数据...")
    
    try:
        # 删除城市数据
        city_count = session.query(City).count()
        if city_count > 0:
            session.query(City).delete()
            print(f"  删除 {city_count} 条城市数据")
        
        # 删除省份数据
        province_count = session.query(Province).count()
        if province_count > 0:
            session.query(Province).delete()
            print(f"  删除 {province_count} 条省份数据")
        
        session.commit()
        print("✅ 现有数据清除完成")
        return True
        
    except Exception as e:
        session.rollback()
        print(f"❌ 清除数据失败: {e}")
        return False


def insert_provinces(session, provinces_data):
    """插入省份数据"""
    print("🏛️ 插入省份数据...")
    
    try:
        current_time = to_utc_for_db()
        inserted_count = 0
        
        for province_info in provinces_data:
            province = Province(
                uuid=uuid.uuid4(),
                code=province_info["code"],
                name=province_info["name"],
                is_active=True,
                is_deleted=False,
                created_at=current_time,
                updated_at=current_time
            )
            session.add(province)
            inserted_count += 1
        
        session.commit()
        print(f"✅ 成功插入 {inserted_count} 个省份")
        return True
        
    except Exception as e:
        session.rollback()
        print(f"❌ 插入省份数据失败: {e}")
        return False


def insert_cities(session, cities_data):
    """插入城市数据"""
    print("🏙️ 插入城市数据...")
    
    try:
        current_time = to_utc_for_db()
        inserted_count = 0
        
        for city_info in cities_data:
            city = City(
                uuid=uuid.uuid4(),
                code=city_info["code"],
                name=city_info["name"],
                province_code=city_info["province_code"],
                is_active=True,
                is_deleted=False,
                created_at=current_time,
                updated_at=current_time
            )
            session.add(city)
            inserted_count += 1
        
        session.commit()
        print(f"✅ 成功插入 {inserted_count} 个城市")
        return True
        
    except Exception as e:
        session.rollback()
        print(f"❌ 插入城市数据失败: {e}")
        return False


def verify_data(session):
    """验证数据完整性"""
    print("🔍 验证数据完整性...")
    
    try:
        # 统计省份数据
        province_count = session.query(Province).filter(Province.is_deleted == False).count()
        print(f"  📊 省份总数: {province_count}")
        
        # 统计城市数据
        city_count = session.query(City).filter(City.is_deleted == False).count()
        print(f"  📊 城市总数: {city_count}")
        
        # 检查每个省份的城市数量
        provinces = session.query(Province).filter(Province.is_deleted == False).all()
        for province in provinces[:10]:  # 只显示前10个省份的统计
            cities_in_province = session.query(City).filter(
                City.province_code == province.code,
                City.is_deleted == False
            ).count()
            print(f"  📍 {province.name}: {cities_in_province} 个城市")
        
        if province_count > 10:
            print(f"  ... 还有 {province_count - 10} 个省份")
        
        # 验证数据关联性
        orphan_cities = session.query(City).filter(
            ~City.province_code.in_(
                session.query(Province.code).filter(Province.is_deleted == False)
            ),
            City.is_deleted == False
        ).count()
        
        if orphan_cities > 0:
            print(f"  ⚠️ 发现 {orphan_cities} 个孤立城市（没有对应省份）")
            return False
        else:
            print("  ✅ 所有城市都有对应的省份")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据验证失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始初始化省市数据...")
    print("=" * 60)
    
    # 获取数据
    print("📥 获取省市数据...")
    provinces_data, cities_data = get_china_provinces_cities_data()
    print(f"  📊 省份数据: {len(provinces_data)} 个")
    print(f"  📊 城市数据: {len(cities_data)} 个")
    
    # 创建数据库会话
    session_local = sessionmaker(bind=engine)
    session = session_local()
    
    try:
        # 清除现有数据
        if not clear_existing_data(session):
            return False
        
        # 插入省份数据
        if not insert_provinces(session, provinces_data):
            return False
        
        # 插入城市数据
        if not insert_cities(session, cities_data):
            return False
        
        # 验证数据
        if not verify_data(session):
            return False
        
        print("\n" + "=" * 60)
        print("🎉 省市数据初始化完成！")
        print("=" * 60)
        
        print("\n📊 数据统计:")
        print(f"✅ 省份: {len(provinces_data)} 个")
        print(f"✅ 城市: {len(cities_data)} 个")
        print("✅ 覆盖范围: 全国34个省级行政区")
        
        print("\n💡 使用说明:")
        print("1. 省份代码为2位数字（如：11=北京，32=江苏）")
        print("2. 城市代码为4位数字（如：1101=北京市，3201=南京市）")
        print("3. 数据基于国家统计局最新行政区划标准")
        
        return True
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return False
        
    finally:
        session.close()


if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ 省市数据初始化失败！")
        sys.exit(1)
