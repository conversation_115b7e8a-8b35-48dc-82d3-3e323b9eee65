#!/usr/bin/env python3
"""
修复车辆ID映射表数据脚本
清理可能存在的UUID格式的错误数据
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.orm import sessionmaker
from app.database import engine
from app.models.chat import CarIdMapping


def is_uuid_format(car_id: str) -> bool:
    """检查是否为UUID格式"""
    return "-" in car_id and len(car_id) > 20


def is_real_car_id(car_id: str) -> bool:
    """检查是否为真实车辆ID"""
    # 真实车辆ID通常是数字格式
    return car_id.isdigit() or (car_id.replace('.', '').isdigit())


def analyze_mapping_data():
    """分析映射表数据"""
    print("🔍 分析车辆ID映射表数据...")
    
    session_local = sessionmaker(bind=engine)
    session = session_local()
    
    try:
        # 查询所有映射记录
        mappings = session.query(CarIdMapping).all()
        
        print(f"📊 总映射记录数: {len(mappings)}")
        
        if not mappings:
            print("✅ 映射表为空，无需修复")
            return True
        
        # 分析数据质量
        valid_mappings = []
        invalid_mappings = []
        
        for mapping in mappings:
            real_car_id = mapping.real_car_id
            
            if is_real_car_id(real_car_id):
                valid_mappings.append(mapping)
            else:
                invalid_mappings.append(mapping)
                print(f"❌ 发现无效映射: real_car_id={real_car_id}, car_uuid={mapping.car_uuid}")
        
        print(f"✅ 有效映射记录: {len(valid_mappings)}")
        print(f"❌ 无效映射记录: {len(invalid_mappings)}")
        
        # 显示详细信息
        if valid_mappings:
            print("\n📋 有效映射记录示例:")
            for i, mapping in enumerate(valid_mappings[:5]):
                print(f"  {i+1}. real_car_id: {mapping.real_car_id}, car_uuid: {mapping.car_uuid}, usage_count: {mapping.usage_count}")
        
        if invalid_mappings:
            print("\n⚠️ 无效映射记录详情:")
            for i, mapping in enumerate(invalid_mappings):
                print(f"  {i+1}. real_car_id: {mapping.real_car_id}, car_uuid: {mapping.car_uuid}")
        
        return True  # 总是返回True，让修复流程继续
        
    except Exception as e:
        print(f"❌ 分析映射数据失败: {e}")
        return False
    finally:
        session.close()


def fix_mapping_data():
    """修复映射表数据"""
    print("\n🔧 开始修复车辆ID映射表数据...")
    
    session_local = sessionmaker(bind=engine)
    session = session_local()
    
    try:
        # 查询所有无效映射记录
        invalid_mappings = []
        mappings = session.query(CarIdMapping).all()
        
        for mapping in mappings:
            if not is_real_car_id(mapping.real_car_id):
                invalid_mappings.append(mapping)
        
        if not invalid_mappings:
            print("✅ 没有发现需要修复的数据")
            return True
        
        print(f"🗑️ 准备删除 {len(invalid_mappings)} 条无效映射记录...")
        
        # 删除无效映射记录
        deleted_count = 0
        for mapping in invalid_mappings:
            print(f"  删除: real_car_id={mapping.real_car_id}")
            session.delete(mapping)
            deleted_count += 1
        
        # 提交更改
        session.commit()
        
        print(f"✅ 成功删除 {deleted_count} 条无效映射记录")
        return True
        
    except Exception as e:
        session.rollback()
        print(f"❌ 修复映射数据失败: {e}")
        return False
    finally:
        session.close()


def verify_fix():
    """验证修复结果"""
    print("\n🔍 验证修复结果...")
    
    session_local = sessionmaker(bind=engine)
    session = session_local()
    
    try:
        # 重新分析数据
        mappings = session.query(CarIdMapping).all()
        
        invalid_count = 0
        for mapping in mappings:
            if not is_real_car_id(mapping.real_car_id):
                invalid_count += 1
        
        if invalid_count == 0:
            print(f"✅ 验证通过！映射表中共有 {len(mappings)} 条有效记录")
            
            # 显示修复后的数据示例
            if mappings:
                print("\n📋 修复后的映射记录示例:")
                for i, mapping in enumerate(mappings[:5]):
                    print(f"  {i+1}. real_car_id: {mapping.real_car_id}, car_uuid: {mapping.car_uuid}")
            
            return True
        else:
            print(f"❌ 验证失败！仍有 {invalid_count} 条无效记录")
            return False
            
    except Exception as e:
        print(f"❌ 验证修复结果失败: {e}")
        return False
    finally:
        session.close()


def create_test_data():
    """创建测试数据验证修复功能"""
    print("\n🧪 创建测试数据...")
    
    session_local = sessionmaker(bind=engine)
    session = session_local()
    
    try:
        # 创建一些测试映射记录
        test_mappings = [
            CarIdMapping(real_car_id="1234567890", car_uuid="550e8400-e29b-41d4-a716-************"),
            CarIdMapping(real_car_id="9876543210", car_uuid="550e8400-e29b-41d4-a716-************"),
        ]
        
        for mapping in test_mappings:
            session.add(mapping)
        
        session.commit()
        print(f"✅ 创建了 {len(test_mappings)} 条测试映射记录")
        return True
        
    except Exception as e:
        session.rollback()
        print(f"❌ 创建测试数据失败: {e}")
        return False
    finally:
        session.close()


def main():
    """主函数"""
    print("🚀 开始车辆ID映射表数据修复...")
    print("=" * 60)
    
    # 步骤1: 分析当前数据
    if not analyze_mapping_data():
        print("❌ 数据分析失败，终止修复")
        return False
    
    # 步骤2: 修复数据
    if not fix_mapping_data():
        print("❌ 数据修复失败")
        return False
    
    # 步骤3: 验证修复结果
    if not verify_fix():
        print("❌ 修复验证失败")
        return False
    
    # 步骤4: 创建测试数据（可选）
    print("\n🧪 是否创建测试数据？(y/n): ", end="")
    try:
        choice = input().strip().lower()
        if choice == 'y':
            create_test_data()
    except KeyboardInterrupt:
        print("\n⏹️ 用户取消操作")
    
    print("\n🎉 车辆ID映射表数据修复完成！")
    return True


if __name__ == "__main__":
    success = main()
    if not success:
        print("❌ 数据修复失败！")
        sys.exit(1)
    else:
        print("✅ 数据修复成功完成！")
