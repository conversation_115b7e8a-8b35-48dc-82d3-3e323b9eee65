#!/usr/bin/env python3
"""
数据库迁移脚本：更新手机号字段长度
将 users 和 sms_codes 表中的 phone 字段从 VARCHAR(11) 更新为 VARCHAR(20)
以支持新的手机号格式 +8613333333333
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.config import settings
from app.database import get_db_url
import structlog

# 配置日志
logger = structlog.get_logger("phone_migration")

# ANSI 颜色代码
GREEN = '\033[92m'
RED = '\033[91m'
YELLOW = '\033[93m'
BLUE = '\033[94m'
RESET = '\033[0m'

def print_success(message):
    print(f"{GREEN}✅ {message}{RESET}")

def print_error(message):
    print(f"{RED}❌ {message}{RESET}")

def print_info(message):
    print(f"{BLUE}ℹ️  {message}{RESET}")

def print_warning(message):
    print(f"{YELLOW}⚠️  {message}{RESET}")

def print_section(title):
    print(f"\n{BLUE}{'='*60}")
    print(f"📋 {title}")
    print(f"{'='*60}{RESET}")

def check_current_schema(engine):
    """检查当前数据库schema"""
    print_section("检查当前数据库结构")
    
    try:
        with engine.connect() as conn:
            # 检查 users 表的 phone 字段
            result = conn.execute(text("""
                SELECT column_name, data_type, character_maximum_length 
                FROM information_schema.columns 
                WHERE table_name = 'users' AND column_name = 'phone'
            """))
            
            users_phone = result.fetchone()
            if users_phone:
                print_info(f"users.phone: {users_phone.data_type}({users_phone.character_maximum_length})")
            else:
                print_warning("users.phone 字段不存在")
            
            # 检查 sms_codes 表的 phone 字段
            result = conn.execute(text("""
                SELECT column_name, data_type, character_maximum_length 
                FROM information_schema.columns 
                WHERE table_name = 'sms_codes' AND column_name = 'phone'
            """))
            
            sms_phone = result.fetchone()
            if sms_phone:
                print_info(f"sms_codes.phone: {sms_phone.data_type}({sms_phone.character_maximum_length})")
            else:
                print_warning("sms_codes.phone 字段不存在")
            
            return users_phone, sms_phone
            
    except Exception as e:
        print_error(f"检查数据库结构失败: {str(e)}")
        return None, None

def backup_data(engine):
    """备份现有数据"""
    print_section("备份现有数据")
    
    try:
        with engine.connect() as conn:
            # 检查 users 表中的数据
            result = conn.execute(text("SELECT COUNT(*) as count FROM users"))
            users_count = result.fetchone().count
            print_info(f"users 表记录数: {users_count}")
            
            # 检查 sms_codes 表中的数据
            result = conn.execute(text("SELECT COUNT(*) as count FROM sms_codes"))
            sms_count = result.fetchone().count
            print_info(f"sms_codes 表记录数: {sms_count}")
            
            # 检查是否有需要转换的手机号数据
            if users_count > 0:
                result = conn.execute(text("SELECT phone FROM users LIMIT 5"))
                sample_phones = result.fetchall()
                print_info("users 表中的手机号样例:")
                for phone in sample_phones:
                    print_info(f"  - {phone.phone}")
            
            if sms_count > 0:
                result = conn.execute(text("SELECT phone FROM sms_codes LIMIT 5"))
                sample_phones = result.fetchall()
                print_info("sms_codes 表中的手机号样例:")
                for phone in sample_phones:
                    print_info(f"  - {phone.phone}")
                    
            return True
            
    except Exception as e:
        print_error(f"备份数据检查失败: {str(e)}")
        return False

def migrate_phone_fields(engine):
    """迁移手机号字段"""
    print_section("执行字段迁移")
    
    try:
        with engine.begin() as conn:
            # 更新 users 表的 phone 字段长度
            print_info("更新 users.phone 字段长度...")
            conn.execute(text("ALTER TABLE users ALTER COLUMN phone TYPE VARCHAR(20)"))
            print_success("users.phone 字段更新完成")
            
            # 更新 sms_codes 表的 phone 字段长度
            print_info("更新 sms_codes.phone 字段长度...")
            conn.execute(text("ALTER TABLE sms_codes ALTER COLUMN phone TYPE VARCHAR(20)"))
            print_success("sms_codes.phone 字段更新完成")
            
            print_success("所有字段迁移完成")
            return True
            
    except Exception as e:
        print_error(f"字段迁移失败: {str(e)}")
        return False

def verify_migration(engine):
    """验证迁移结果"""
    print_section("验证迁移结果")
    
    try:
        with engine.connect() as conn:
            # 验证 users 表的 phone 字段
            result = conn.execute(text("""
                SELECT column_name, data_type, character_maximum_length 
                FROM information_schema.columns 
                WHERE table_name = 'users' AND column_name = 'phone'
            """))
            
            users_phone = result.fetchone()
            if users_phone and users_phone.character_maximum_length == 20:
                print_success(f"users.phone: {users_phone.data_type}({users_phone.character_maximum_length}) ✓")
            else:
                print_error(f"users.phone 迁移失败")
                return False
            
            # 验证 sms_codes 表的 phone 字段
            result = conn.execute(text("""
                SELECT column_name, data_type, character_maximum_length 
                FROM information_schema.columns 
                WHERE table_name = 'sms_codes' AND column_name = 'phone'
            """))
            
            sms_phone = result.fetchone()
            if sms_phone and sms_phone.character_maximum_length == 20:
                print_success(f"sms_codes.phone: {sms_phone.data_type}({sms_phone.character_maximum_length}) ✓")
            else:
                print_error(f"sms_codes.phone 迁移失败")
                return False
            
            # 测试插入新格式手机号
            print_info("测试新格式手机号插入...")
            test_phone = "+8613333333333"
            
            # 这里只是验证字段长度，不实际插入数据
            if len(test_phone) <= 20:
                print_success(f"新格式手机号 {test_phone} 可以正常存储")
            else:
                print_error(f"新格式手机号 {test_phone} 长度超出限制")
                return False
            
            return True
            
    except Exception as e:
        print_error(f"验证迁移结果失败: {str(e)}")
        return False

def main():
    """主函数"""
    print(f"{BLUE}📱 手机号字段迁移脚本{RESET}")
    print(f"{BLUE}目标: 将 phone 字段从 VARCHAR(11) 更新为 VARCHAR(20){RESET}")
    print(f"{BLUE}支持新格式: +8613333333333{RESET}")
    
    try:
        # 创建数据库连接
        database_url = get_db_url()
        engine = create_engine(database_url)
        
        print_info(f"连接数据库: {database_url.split('@')[1] if '@' in database_url else 'localhost'}")
        
        # 1. 检查当前schema
        users_phone, sms_phone = check_current_schema(engine)
        
        if not users_phone or not sms_phone:
            print_error("无法获取当前数据库结构，迁移终止")
            return False
        
        # 检查是否需要迁移
        need_migration = False
        if users_phone.character_maximum_length != 20:
            print_warning(f"users.phone 需要迁移: {users_phone.character_maximum_length} -> 20")
            need_migration = True
        
        if sms_phone.character_maximum_length != 20:
            print_warning(f"sms_codes.phone 需要迁移: {sms_phone.character_maximum_length} -> 20")
            need_migration = True
        
        if not need_migration:
            print_success("字段长度已经是20，无需迁移")
            return True
        
        # 2. 备份数据检查
        if not backup_data(engine):
            print_error("数据备份检查失败，迁移终止")
            return False
        
        # 3. 确认迁移
        print_warning("\n即将执行数据库迁移，这将修改表结构")
        response = input("是否继续？(y/N): ").strip().lower()
        
        if response != 'y':
            print_info("迁移已取消")
            return False
        
        # 4. 执行迁移
        if not migrate_phone_fields(engine):
            print_error("字段迁移失败")
            return False
        
        # 5. 验证迁移结果
        if not verify_migration(engine):
            print_error("迁移验证失败")
            return False
        
        print_section("迁移完成")
        print_success("手机号字段迁移成功完成！")
        print_info("现在可以使用新格式的手机号: +8613333333333")
        
        return True
        
    except Exception as e:
        print_error(f"迁移过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print(f"\n{YELLOW}迁移被用户中断{RESET}")
        sys.exit(1)
    except Exception as e:
        print_error(f"脚本执行失败: {str(e)}")
        sys.exit(1)
