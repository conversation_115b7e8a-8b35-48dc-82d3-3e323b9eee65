#!/usr/bin/env python3
"""
聊天相关表索引优化脚本
为对话历史查询添加高效的复合索引
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import create_engine, text, inspect
from app.config import settings
import structlog

logger = structlog.get_logger("optimize_chat_indexes")


def check_existing_indexes():
    """检查现有索引"""
    print("🔍 检查现有索引...")
    
    try:
        engine = create_engine(settings.database_url)
        inspector = inspect(engine)
        
        tables = ['chat_messages', 'car_recommendations', 'car_comparisons', 'ai_conversations']
        
        for table_name in tables:
            print(f"\n📋 {table_name} 表的现有索引:")
            try:
                indexes = inspector.get_indexes(table_name)
                if indexes:
                    for idx in indexes:
                        print(f"  - {idx['name']}: {idx['column_names']}")
                else:
                    print("  - 无索引")
            except Exception as e:
                print(f"  ❌ 检查失败: {e}")
        
        return True
        
    except Exception as e:
        logger.error("检查现有索引失败", error=str(e))
        return False


def create_optimized_indexes():
    """创建优化的复合索引"""
    print("\n🚀 创建优化索引...")
    
    try:
        engine = create_engine(settings.database_url)
        
        with engine.connect() as conn:
            # 对话历史查询优化索引
            print("  📊 创建对话历史查询索引...")
            
            # 1. 聊天消息表 - 按对话ID和时间排序的复合索引
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_chat_messages_conversation_time_asc 
                ON chat_messages(conversation_id, created_at ASC) 
                WHERE is_deleted = false;
            """))
            
            # 2. 聊天消息表 - 按对话ID和时间倒序的复合索引
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_chat_messages_conversation_time_desc 
                ON chat_messages(conversation_id, created_at DESC) 
                WHERE is_deleted = false;
            """))
            
            # 3. 聊天消息表 - 按用户ID和时间的复合索引
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_chat_messages_user_time 
                ON chat_messages(user_id, created_at DESC) 
                WHERE is_deleted = false;
            """))
            
            # 4. 车辆推荐表 - 按对话ID和消息UUID的复合索引
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_car_recommendations_conv_msg 
                ON car_recommendations(conversation_id, chat_message_uuid) 
                WHERE is_deleted = false;
            """))
            
            # 5. 车辆推荐表 - 按消息ID和任务类型的复合索引
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_car_recommendations_msg_task 
                ON car_recommendations(message_id, task_type) 
                WHERE is_deleted = false;
            """))
            
            # 6. 车辆对比表 - 按对话ID和消息UUID的复合索引
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_car_comparisons_conv_msg 
                ON car_comparisons(conversation_id, chat_message_uuid) 
                WHERE is_deleted = false;
            """))
            
            # 7. 车辆对比表 - 按消息ID和任务类型的复合索引
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_car_comparisons_msg_task 
                ON car_comparisons(message_id, task_type) 
                WHERE is_deleted = false;
            """))
            
            # 8. AI对话表 - 按用户ID和更新时间的复合索引
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_ai_conversations_user_updated 
                ON ai_conversations(user_id, updated_at DESC) 
                WHERE is_deleted = false;
            """))
            
            # 9. 聊天消息表 - 按消息类型和对话ID的复合索引
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_chat_messages_type_conversation 
                ON chat_messages(message_type, conversation_id, created_at DESC) 
                WHERE is_deleted = false;
            """))
            
            # 10. 车辆推荐表 - 按创建时间的索引（用于最近推荐查询）
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_car_recommendations_created_time 
                ON car_recommendations(created_at DESC) 
                WHERE is_deleted = false;
            """))
            
            conn.commit()
            print("  ✅ 优化索引创建完成")
            
        return True
        
    except Exception as e:
        logger.error("创建优化索引失败", error=str(e))
        return False


def analyze_query_performance():
    """分析查询性能"""
    print("\n📈 分析查询性能...")
    
    try:
        engine = create_engine(settings.database_url)
        
        with engine.connect() as conn:
            # 测试对话历史查询性能
            print("  🔍 测试对话历史查询...")
            
            # 模拟对话历史查询
            result = conn.execute(text("""
                EXPLAIN ANALYZE 
                SELECT cm.*, cr.uuid as rec_uuid, cc.uuid as comp_uuid
                FROM chat_messages cm
                LEFT JOIN car_recommendations cr ON cm.uuid = cr.chat_message_uuid AND cr.is_deleted = false
                LEFT JOIN car_comparisons cc ON cm.uuid = cc.chat_message_uuid AND cc.is_deleted = false
                WHERE cm.conversation_id = '00000000-0000-0000-0000-000000000000'::uuid
                AND cm.is_deleted = false
                ORDER BY cm.created_at ASC
                LIMIT 50;
            """))
            
            print("  📊 查询执行计划:")
            for row in result:
                print(f"    {row[0]}")
            
        return True
        
    except Exception as e:
        logger.error("分析查询性能失败", error=str(e))
        return False


def verify_indexes():
    """验证索引创建结果"""
    print("\n✅ 验证索引创建结果...")
    
    try:
        engine = create_engine(settings.database_url)
        
        with engine.connect() as conn:
            # 检查新创建的索引
            result = conn.execute(text("""
                SELECT 
                    schemaname,
                    tablename,
                    indexname,
                    indexdef
                FROM pg_indexes 
                WHERE tablename IN ('chat_messages', 'car_recommendations', 'car_comparisons', 'ai_conversations')
                AND indexname LIKE 'idx_%'
                ORDER BY tablename, indexname;
            """))
            
            indexes_by_table = {}
            for row in result:
                table = row[1]
                if table not in indexes_by_table:
                    indexes_by_table[table] = []
                indexes_by_table[table].append({
                    'name': row[2],
                    'definition': row[3]
                })
            
            for table, indexes in indexes_by_table.items():
                print(f"\n📋 {table} 表索引 ({len(indexes)} 个):")
                for idx in indexes:
                    print(f"  ✓ {idx['name']}")
            
        return True
        
    except Exception as e:
        logger.error("验证索引失败", error=str(e))
        return False


def main():
    """主函数"""
    print("🚀 开始聊天索引优化...")
    print("=" * 60)
    
    # 步骤1: 检查现有索引
    if not check_existing_indexes():
        print("❌ 检查现有索引失败")
        return False
    
    # 步骤2: 创建优化索引
    if not create_optimized_indexes():
        print("❌ 创建优化索引失败")
        return False
    
    # 步骤3: 验证索引
    if not verify_indexes():
        print("❌ 验证索引失败")
        return False
    
    # 步骤4: 分析性能
    if not analyze_query_performance():
        print("⚠️ 性能分析失败，但索引已创建")
    
    print("\n🎉 聊天索引优化完成！")
    print("📊 建议定期运行 ANALYZE 命令更新统计信息")
    return True


if __name__ == "__main__":
    success = main()
    if not success:
        print("❌ 索引优化失败！")
        sys.exit(1)
