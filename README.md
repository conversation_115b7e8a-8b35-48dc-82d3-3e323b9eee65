# FastAPI 服务器项目

一个功能完整的 FastAPI 服务器项目，包含用户管理、物品管理、详细日志记录等功能。

## 🚀 项目特性

### 核心功能
- **手机号验证码登录**：短信验证码发送、验证码登录、JWT 令牌认证
- **用户信息管理**：用户资料查询、用户名设置、头像上传、地址管理
- **省市数据管理**：完整的省市数据、地址选择功能
- **文件上传系统**：头像上传、文件验证、静态文件服务
- **详细日志系统**：结构化日志、请求追踪、性能监控、安全审计

### 技术特性
- **现代 Python**：基于 Python 3.9+ 和 FastAPI 框架
- **数据库支持**：PostgreSQL 数据库，SQLAlchemy ORM
- **数据验证**：Pydantic 模型，自动 API 文档生成
- **异步支持**：全异步架构，高性能处理
- **容器化**：Docker 支持，易于部署
- **测试覆盖**：完整的单元测试和集成测试

### 架构特性
- **分层架构**：清晰的代码组织结构
- **模块化设计**：可扩展的组件化架构
- **错误处理**：统一的异常处理机制
- **配置管理**：环境变量配置，多环境支持
- **监控健康**：健康检查、指标收集、性能监控

## 📁 项目结构

```
server/
├── app/                          # 主应用目录
│   ├── __init__.py
│   ├── main.py                   # FastAPI 应用入口
│   ├── config.py                 # 配置管理
│   ├── logging_config.py         # 日志配置
│   ├── middleware.py             # 中间件
│   ├── dependencies.py           # 全局依赖
│   ├── api/                      # API 路由
│   │   ├── v1/
│   │   │   ├── api.py           # 路由聚合
│   │   │   └── endpoints/       # 具体端点
│   │   │       ├── health.py    # 健康检查
│   │   │       ├── users.py     # 用户管理
│   │   │       └── items.py     # 物品管理
│   ├── core/                     # 核心功能
│   │   ├── exceptions.py         # 异常处理
│   │   └── security.py          # 安全相关
│   ├── models/                   # 数据模型
│   │   ├── user.py              # 用户模型
│   │   └── item.py              # 物品模型
│   ├── schemas/                  # Pydantic 模型
│   │   ├── response.py          # 响应模型
│   │   ├── user.py              # 用户模式
│   │   └── item.py              # 物品模式
│   ├── services/                 # 业务服务
│   │   ├── user_service.py      # 用户服务
│   │   └── item_service.py      # 物品服务
│   └── utils/                    # 工具类
│       ├── logger.py            # 日志工具
│       └── helpers.py           # 辅助函数
├── tests/                        # 测试目录
│   ├── __init__.py
│   └── test_main.py             # 主要测试
├── logs/                         # 日志文件目录
├── requirements.txt              # 项目依赖
├── .env                         # 环境变量
├── .gitignore                   # Git 忽略文件
├── README.md                    # 项目说明
└── uvicorn_config.py            # 服务器配置
```

## 🛠️ 快速开始

### 环境要求

- Python 3.9+
- PostgreSQL 12+
- Git

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd server
```

2. **创建虚拟环境**
```bash
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# 或
.venv\Scripts\activate     # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息
```

5. **配置数据库**
```bash
# 确保 PostgreSQL 服务运行
# 创建数据库
createdb fastapi_db

# 运行数据库迁移（如果有的话）
# alembic upgrade head
```

6. **初始化数据库和启动服务**
```bash
# 一键启动（包含数据库初始化）
python run.py

# 或分步执行
python run.py --init-db  # 仅初始化数据库
python uvicorn_config.py run  # 仅启动服务
```

7. **访问应用**
- API 文档：http://localhost:8000/docs
- ReDoc 文档：http://localhost:8000/redoc
- 健康检查：http://localhost:8000/health

## 🔧 配置说明

### 环境变量

主要的环境变量配置：

```bash
# 应用配置
APP_NAME=FastAPI Server
APP_VERSION=1.0.0
DEBUG=true
ENVIRONMENT=development

# 服务器配置
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/fastapi_db
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=fastapi_db
DATABASE_USER=username
DATABASE_PASSWORD=password

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE_ENABLED=true

# 安全配置
SECRET_KEY=your-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

### 日志配置

项目支持多种日志配置：

- **日志级别**：DEBUG, INFO, WARNING, ERROR, CRITICAL
- **日志格式**：JSON 或文本格式
- **输出方式**：控制台、文件、或两者
- **文件轮转**：自动按大小轮转日志文件
- **结构化日志**：包含请求ID、用户ID等上下文信息

## 📚 API 文档

### 主要端点

#### 健康检查
- `GET /health` - 基础健康检查
- `GET /api/v1/health/detailed` - 详细健康检查
- `GET /api/v1/health/metrics` - 应用指标

#### 认证管理
- `POST /api/v1/auth/send-code` - 发送验证码
- `POST /api/v1/auth/login` - 验证码登录
- `POST /api/v1/auth/refresh` - 刷新令牌
- `POST /api/v1/auth/logout` - 用户登出

#### 用户信息管理
- `GET /api/v1/user/profile` - 获取用户资料
- `POST /api/v1/user/update-username` - 更新用户名
- `POST /api/v1/user/avatar` - 上传头像
- `POST /api/v1/user/update-address` - 更新地址

#### 地址数据
- `GET /api/v1/user/provinces` - 获取省份列表
- `GET /api/v1/user/cities/{province_code}` - 获取城市列表

### 认证方式

API 使用手机号验证码登录和 Bearer Token 认证：

```bash
# 1. 发送验证码
curl -X POST "http://localhost:8000/api/v1/auth/send-code" \
     -H "Content-Type: application/json" \
     -d '{"phone": "13800138000", "purpose": "login"}'

# 2. 验证码登录获取令牌
curl -X POST "http://localhost:8000/api/v1/auth/login" \
     -H "Content-Type: application/json" \
     -d '{"phone": "13800138000", "code": "123456"}'

# 3. 使用令牌访问受保护的端点
curl -X GET "http://localhost:8000/api/v1/user/profile" \
     -H "Authorization: Bearer your_access_token"
```

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_main.py

# 运行测试并生成覆盖率报告
pytest --cov=app

# 详细输出
pytest -v
```

### 测试覆盖

项目包含以下测试：

- **单元测试**：测试各个组件的功能
- **集成测试**：测试 API 端点的完整流程
- **中间件测试**：测试请求处理中间件
- **错误处理测试**：测试异常情况的处理

## 🚀 部署

### Docker 部署

1. **构建镜像**
```bash
docker build -t fastapi-server .
```

2. **运行容器**
```bash
docker run -d \
  --name fastapi-server \
  -p 8000:8000 \
  -e DATABASE_URL=********************************/db \
  fastapi-server
```

### 生产环境部署

1. **使用 Gunicorn + Uvicorn**
```bash
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker
```

2. **环境变量配置**
```bash
export ENVIRONMENT=production
export DEBUG=false
export LOG_LEVEL=WARNING
```

## 📊 监控和日志

### 日志查看

```bash
# 查看应用日志
tail -f logs/fastapi_server.log

# 查看结构化日志
cat logs/fastapi_server.log | jq '.'
```

### 健康检查

```bash
# 基础健康检查
curl http://localhost:8000/health

# 详细健康检查
curl http://localhost:8000/api/v1/health/detailed

# 就绪检查
curl http://localhost:8000/api/v1/health/readiness

# 存活检查
curl http://localhost:8000/api/v1/health/liveness
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如果您有任何问题或建议，请：

1. 查看 [文档](http://localhost:8000/docs)
2. 提交 [Issue](https://github.com/your-repo/issues)
3. 联系维护者

## 🔄 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 用户管理系统
- 物品管理系统
- 详细日志记录
- 完整的 API 文档
- 测试覆盖
