"""
AI对话会话服务
"""

from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_
import structlog

from app.models.conversation import AIConversation
from app.models.user import User
from app.schemas.conversation import CreateConversationRequest, DeleteConversationRequest
from app.database import SessionLocal
from app.core.exceptions import BusinessLogicError, ValidationError
from app.utils.logger import BusinessLogger


class ConversationService:
    """AI对话会话服务"""

    def __init__(self):
        self.logger = BusinessLogger("conversation_service")
        self.struct_logger = structlog.get_logger("conversation_service")

    def create_conversation(
        self,
        request_data: CreateConversationRequest,
        user_id: int
    ) -> Dict[str, Any]:
        """
        创建新的AI对话会话
        
        Args:
            request_data: 创建会话请求数据
            user_id: 用户ID
            
        Returns:
            创建的会话信息
            
        Raises:
            ValidationError: 请求数据验证失败
            BusinessLogicError: 业务逻辑错误
        """
        db = SessionLocal()
        try:
            # 验证用户是否存在且有效
            user = db.query(User).filter(
                and_(
                    User.id == user_id,
                    User.is_deleted == False,
                    User.is_active == True
                )
            ).first()
            
            if not user:
                raise BusinessLogicError("用户不存在或已被禁用")

            # 创建新会话
            conversation = AIConversation(
                user_id=user_id,
                title=request_data.title.strip()
            )
            
            db.add(conversation)
            db.commit()
            db.refresh(conversation)
            
            self.struct_logger.info(
                "AI会话创建成功",
                conversation_id=str(conversation.uuid),
                user_id=user_id,
                title=conversation.title
            )
            
            return conversation.to_dict()
            
        except Exception as e:
            db.rollback()
            self.struct_logger.error(
                "创建AI会话失败",
                user_id=user_id,
                title=request_data.title,
                error=str(e)
            )
            raise
        finally:
            db.close()

    def get_user_conversations(self, user_id: int) -> List[Dict[str, Any]]:
        """
        获取用户的所有未删除会话列表
        
        Args:
            user_id: 用户ID
            
        Returns:
            会话列表
            
        Raises:
            BusinessLogicError: 业务逻辑错误
        """
        db = SessionLocal()
        try:
            # 验证用户是否存在且有效
            user = db.query(User).filter(
                and_(
                    User.id == user_id,
                    User.is_deleted == False,
                    User.is_active == True
                )
            ).first()
            
            if not user:
                raise BusinessLogicError("用户不存在或已被禁用")

            # 查询用户的所有未删除会话，按创建时间倒序
            conversations = db.query(AIConversation).filter(
                and_(
                    AIConversation.user_id == user_id,
                    AIConversation.is_deleted == False
                )
            ).order_by(AIConversation.created_at.desc()).all()
            
            # 转换为列表项格式（不包含敏感信息）
            conversation_list = []
            for conv in conversations:
                conversation_list.append({
                    "id": conv.id,
                    "conversation_id": str(conv.uuid),
                    "title": conv.title,
                    "created_at": conv.created_at.isoformat() if conv.created_at else None,
                    "updated_at": conv.updated_at.isoformat() if conv.updated_at else None
                })
            
            self.struct_logger.info(
                "获取用户会话列表成功",
                user_id=user_id,
                conversation_count=len(conversation_list)
            )
            
            return conversation_list
            
        except Exception as e:
            self.struct_logger.error(
                "获取用户会话列表失败",
                user_id=user_id,
                error=str(e)
            )
            raise
        finally:
            db.close()

    def get_conversation_by_id(
        self,
        conversation_id: str,
        user_id: int
    ) -> Optional[Dict[str, Any]]:
        """
        根据会话ID获取会话详情
        
        Args:
            conversation_id: 会话UUID
            user_id: 用户ID
            
        Returns:
            会话详情或None
        """
        db = SessionLocal()
        try:
            conversation = db.query(AIConversation).filter(
                and_(
                    AIConversation.uuid == conversation_id,
                    AIConversation.user_id == user_id,
                    AIConversation.is_deleted == False
                )
            ).first()
            
            if conversation:
                return conversation.to_dict()
            return None
            
        except Exception as e:
            self.struct_logger.error(
                "获取会话详情失败",
                conversation_id=conversation_id,
                user_id=user_id,
                error=str(e)
            )
            raise
        finally:
            db.close()

    def delete_conversation(
        self,
        request_data: DeleteConversationRequest,
        user_id: int
    ) -> Dict[str, Any]:
        """
        删除指定的AI对话会话（软删除）

        Args:
            request_data: 删除会话请求数据
            user_id: 用户ID

        Returns:
            删除操作结果

        Raises:
            ValidationError: 请求数据验证失败
            BusinessLogicError: 业务逻辑错误（会话不存在或无权限）
        """
        db = SessionLocal()
        try:
            # 验证用户是否存在且有效
            user = db.query(User).filter(
                and_(
                    User.id == user_id,
                    User.is_deleted == False,
                    User.is_active == True
                )
            ).first()

            if not user:
                raise BusinessLogicError("用户不存在或已被禁用")

            # 查找指定的会话
            conversation = db.query(AIConversation).filter(
                and_(
                    AIConversation.uuid == request_data.conversation_id,
                    AIConversation.user_id == user_id,
                    AIConversation.is_deleted == False
                )
            ).first()

            if not conversation:
                raise BusinessLogicError("会话不存在或您无权限删除此会话")

            # 执行软删除
            conversation.soft_delete()

            db.commit()
            db.refresh(conversation)

            self.struct_logger.info(
                "AI会话删除成功",
                conversation_id=str(conversation.uuid),
                user_id=user_id,
                title=conversation.title
            )

            return {
                "conversation_id": str(conversation.uuid),
                "title": conversation.title,
                "deleted_at": conversation.deleted_at.isoformat() if conversation.deleted_at else None,
                "message": "会话删除成功"
            }

        except Exception as e:
            db.rollback()
            self.struct_logger.error(
                "删除AI会话失败",
                user_id=user_id,
                conversation_id=request_data.conversation_id,
                error=str(e)
            )
            raise
        finally:
            db.close()
