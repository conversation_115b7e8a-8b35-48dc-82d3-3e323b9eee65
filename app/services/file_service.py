"""
文件上传服务
处理文件上传、存储等功能
"""

import os
import uuid
from typing import Optional, Dict, Any
from pathlib import Path
import aiofiles
from fastapi import UploadFile
import structlog

from app.config import settings
from app.utils.logger import BusinessLogger
from app.core.exceptions import BusinessLogicError, ValidationError
from app.utils.helpers import FileHelper


class FileService:
    """文件服务类"""
    
    def __init__(self):
        self.logger = BusinessLogger("file_service")
        self.struct_logger = structlog.get_logger("file_service")
        
        # 上传目录配置
        self.upload_dir = Path("uploads")
        self.avatar_dir = self.upload_dir / "avatars"
        
        # 创建上传目录
        self._ensure_upload_dirs()
        
        # 文件配置
        self.max_file_size = 5 * 1024 * 1024  # 5MB
        self.allowed_image_types = {
            "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"
        }
        self.allowed_extensions = {".jpg", ".jpeg", ".png", ".gif", ".webp"}
    
    def _ensure_upload_dirs(self):
        """确保上传目录存在"""
        try:
            self.upload_dir.mkdir(exist_ok=True)
            self.avatar_dir.mkdir(exist_ok=True)
            self.struct_logger.info("上传目录初始化完成")
        except Exception as e:
            self.struct_logger.error("创建上传目录失败", error=str(e))
            raise BusinessLogicError("文件服务初始化失败")
    
    async def upload_avatar(
        self, 
        file: UploadFile, 
        user_id: str
    ) -> Dict[str, Any]:
        """上传用户头像"""
        try:
            # 验证文件
            await self._validate_image_file(file)
            
            # 生成文件名（使用纯UUID，不包含用户ID）
            file_extension = FileHelper.get_file_extension(file.filename)
            filename = f"{uuid.uuid4().hex}{file_extension}"
            file_path = self.avatar_dir / filename
            
            # 保存文件
            await self._save_file(file, file_path)
            
            # 生成访问URL（只存储文件名）
            avatar_url = filename
            
            self.logger.log_operation(
                operation="upload_avatar",
                user_id=user_id,
                details={
                    "filename": filename,
                    "file_size": file.size,
                    "content_type": file.content_type
                },
                success=True
            )
            
            return {
                "success": True,
                "message": "头像上传成功",
                "avatar_url": avatar_url,
                "filename": filename
            }
            
        except Exception as e:
            self.logger.log_operation(
                operation="upload_avatar",
                user_id=user_id,
                details={"error": str(e)},
                success=False
            )
            raise
    
    async def _validate_image_file(self, file: UploadFile):
        """验证图片文件"""
        # 检查文件大小
        if file.size > self.max_file_size:
            raise ValidationError(
                f"文件大小不能超过 {FileHelper.format_file_size(self.max_file_size)}",
                "file_size",
                file.size
            )
        
        # 检查文件类型
        if file.content_type not in self.allowed_image_types:
            raise ValidationError(
                "不支持的文件类型，请上传 JPG、PNG、GIF 或 WebP 格式的图片",
                "content_type",
                file.content_type
            )
        
        # 检查文件扩展名
        if not file.filename:
            raise ValidationError("文件名不能为空", "filename", file.filename)
        
        file_extension = FileHelper.get_file_extension(file.filename)
        if file_extension not in self.allowed_extensions:
            raise ValidationError(
                "不支持的文件扩展名",
                "file_extension",
                file_extension
            )
        
        # 检查文件内容（读取文件头）
        await self._validate_image_content(file)
    
    async def _validate_image_content(self, file: UploadFile):
        """验证图片文件内容"""
        try:
            # 读取文件头部字节
            await file.seek(0)
            header = await file.read(16)
            await file.seek(0)  # 重置文件指针
            
            # 检查文件签名
            image_signatures = {
                b'\xff\xd8\xff': 'JPEG',
                b'\x89\x50\x4e\x47\x0d\x0a\x1a\x0a': 'PNG',
                b'\x47\x49\x46\x38': 'GIF',
                b'\x52\x49\x46\x46': 'WebP'
            }
            
            is_valid_image = False
            for signature, format_name in image_signatures.items():
                if header.startswith(signature):
                    is_valid_image = True
                    break
            
            if not is_valid_image:
                raise ValidationError(
                    "文件内容不是有效的图片格式",
                    "file_content",
                    "invalid_image"
                )
                
        except Exception as e:
            if isinstance(e, ValidationError):
                raise
            self.struct_logger.error("验证图片内容失败", error=str(e))
            raise ValidationError("文件验证失败", "file_validation", str(e))
    
    async def _save_file(self, file: UploadFile, file_path: Path):
        """保存文件到磁盘"""
        try:
            async with aiofiles.open(file_path, 'wb') as f:
                # 分块读取和写入文件
                chunk_size = 8192  # 8KB
                while chunk := await file.read(chunk_size):
                    await f.write(chunk)
            
            self.struct_logger.info("文件保存成功", file_path=str(file_path))
            
        except Exception as e:
            # 如果保存失败，删除可能创建的文件
            if file_path.exists():
                try:
                    file_path.unlink()
                except:
                    pass
            
            self.struct_logger.error("文件保存失败", file_path=str(file_path), error=str(e))
            raise BusinessLogicError("文件保存失败")

    async def get_avatar_file(self, file_name: str) -> Optional[str]:
        """获取头像文件路径"""
        try:
            # 验证文件名安全性
            if ".." in file_name or "/" in file_name or "\\" in file_name:
                self.struct_logger.warning("检测到不安全的文件名", file_name=file_name)
                return None

            # 构建文件路径
            file_path = self.avatar_dir / file_name

            # 检查文件是否存在且在正确的目录中
            if file_path.exists() and file_path.is_file():
                # 确保文件在头像目录中（防止路径遍历）
                try:
                    file_path.resolve().relative_to(self.avatar_dir.resolve())
                    return str(file_path)
                except ValueError:
                    self.struct_logger.warning("文件路径不在允许的目录中", file_name=file_name)
                    return None

            return None

        except Exception as e:
            self.struct_logger.error("获取头像文件失败", file_name=file_name, error=str(e))
            return None

    async def delete_file(self, file_path: str) -> bool:
        """删除文件"""
        try:
            full_path = self.upload_dir / file_path.lstrip('/')
            
            if full_path.exists() and full_path.is_file():
                full_path.unlink()
                self.struct_logger.info("文件删除成功", file_path=str(full_path))
                return True
            else:
                self.struct_logger.warning("文件不存在", file_path=str(full_path))
                return False
                
        except Exception as e:
            self.struct_logger.error("文件删除失败", file_path=file_path, error=str(e))
            return False
    
    def get_file_info(self, file_path: str) -> Optional[Dict[str, Any]]:
        """获取文件信息"""
        try:
            full_path = self.upload_dir / file_path.lstrip('/')
            
            if not full_path.exists():
                return None
            
            stat = full_path.stat()
            
            return {
                "path": file_path,
                "size": stat.st_size,
                "size_formatted": FileHelper.format_file_size(stat.st_size),
                "created_at": stat.st_ctime,
                "modified_at": stat.st_mtime,
                "is_file": full_path.is_file(),
                "extension": FileHelper.get_file_extension(str(full_path))
            }
            
        except Exception as e:
            self.struct_logger.error("获取文件信息失败", file_path=file_path, error=str(e))
            return None
    
    def cleanup_old_files(self, days: int = 30):
        """清理旧文件"""
        try:
            import time
            from datetime import datetime, timedelta
            
            cutoff_time = time.time() - (days * 24 * 60 * 60)
            deleted_count = 0
            
            for file_path in self.upload_dir.rglob("*"):
                if file_path.is_file():
                    if file_path.stat().st_mtime < cutoff_time:
                        try:
                            file_path.unlink()
                            deleted_count += 1
                        except Exception as e:
                            self.struct_logger.error(
                                "删除旧文件失败", 
                                file_path=str(file_path), 
                                error=str(e)
                            )
            
            self.struct_logger.info("旧文件清理完成", deleted_count=deleted_count, days=days)
            return deleted_count
            
        except Exception as e:
            self.struct_logger.error("清理旧文件失败", error=str(e))
            return 0
