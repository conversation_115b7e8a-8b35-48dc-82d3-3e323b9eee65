"""
认证服务
处理验证码发送、用户登录等认证相关业务逻辑
"""

import random
import string
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
import structlog

from app.models.user import User, SMSCode
from app.schemas.auth import SendCodeRequest, LoginRequest, TokenPayload
from app.database import get_db
from app.config import settings
from app.utils.logger import BusinessLogger, SecurityLogger
from app.core.exceptions import BusinessLogicError, AuthenticationError
from app.core.security import create_access_token, verify_token
from app.services.token_service import TokenService


class AuthService:
    """认证服务类"""
    
    def __init__(self):
        self.logger = BusinessLogger("auth_service")
        self.security_logger = SecurityLogger()
        self.struct_logger = structlog.get_logger("auth_service")
        self.token_service = TokenService()
    
    async def send_verification_code(
        self, 
        request: SendCodeRequest, 
        client_ip: str = None
    ) -> Dict[str, Any]:
        """发送验证码"""
        try:
            # 生成验证码
            code = self._generate_verification_code()
            
            # 设置过期时间（5分钟）
            from app.utils.timezone import get_expiry_time
            expires_at = get_expiry_time(5)
            
            # 保存验证码到数据库
            db = next(get_db())
            try:
                # 软删除该手机号之前未使用的验证码
                old_codes = db.query(SMSCode).filter(
                    SMSCode.phone == request.phone,
                    SMSCode.purpose == request.purpose,
                    SMSCode.is_used == False,
                    SMSCode.is_deleted == False
                ).all()

                for old_code in old_codes:
                    old_code.soft_delete()
                
                # 创建新的验证码记录
                sms_code = SMSCode(
                    phone=request.phone,
                    code=code,
                    purpose=request.purpose,
                    expires_at=expires_at
                )
                db.add(sms_code)
                db.commit()
                
                # 记录业务日志
                self.logger.log_operation(
                    operation="send_verification_code",
                    details={
                        "phone": request.phone,
                        "purpose": request.purpose,
                        "client_ip": client_ip
                    },
                    success=True
                )
                
                # 发送短信（预留接口）
                await self._send_sms(request.phone, code, request.purpose)
                
                # 构建响应
                response = {
                    "success": True,
                    "message": "验证码发送成功",
                    "expires_in": 300  # 5分钟
                }
                
                # 测试环境返回验证码
                if settings.is_development:
                    response["code"] = code
                
                return response
                
            finally:
                db.close()
                
        except Exception as e:
            self.logger.log_operation(
                operation="send_verification_code",
                details={
                    "phone": request.phone,
                    "error": str(e)
                },
                success=False
            )
            raise BusinessLogicError(f"发送验证码失败: {str(e)}")
    
    async def login_with_code(
        self, 
        request: LoginRequest, 
        client_ip: str = None
    ) -> Dict[str, Any]:
        """验证码登录"""
        try:
            db = next(get_db())
            try:
                # 验证验证码
                if not await self._verify_code(db, request.phone, request.code, "login"):
                    self.security_logger.log_authentication(
                        user_id=request.phone,
                        success=False,
                        method="sms_code",
                        client_ip=client_ip,
                        details={"reason": "invalid_code"}
                    )
                    raise AuthenticationError("验证码错误或已过期")
                
                # 查找或创建用户
                user = db.query(User).filter(
                    User.phone == request.phone,
                    User.is_deleted == False
                ).first()

                if not user:
                    # 创建新用户
                    user = User(phone=request.phone, is_active=True)
                    db.add(user)
                    db.commit()
                    db.refresh(user)

                    self.struct_logger.info("新用户注册", user_id=str(user.uuid), phone=request.phone)
                
                # 检查用户状态
                if not user.is_active:
                    self.security_logger.log_authentication(
                        user_id=str(user.id),
                        success=False,
                        method="sms_code",
                        client_ip=client_ip,
                        details={"reason": "account_inactive"}
                    )
                    raise AuthenticationError("账户已被禁用")
                
                # 更新最后登录时间
                user.update_last_login()
                db.commit()
                
                # 创建令牌对
                token_result = await self.token_service.create_token_pair(
                    user=user,
                    client_ip=client_ip,
                    device_name="Web Browser",  # 可以从 user_agent 解析
                    user_agent=None  # 可以从请求头获取
                )
                
                # 记录成功登录
                self.security_logger.log_authentication(
                    user_id=str(user.uuid),
                    success=True,
                    method="sms_code",
                    client_ip=client_ip
                )

                self.logger.log_operation(
                    operation="login",
                    user_id=str(user.uuid),
                    details={"phone": request.phone},
                    success=True
                )

                return {
                    "success": True,
                    "message": "登录成功",
                    "access_token": token_result["access_token"],
                    "refresh_token": token_result["refresh_token"],
                    "token_type": token_result["token_type"],
                    "expires_in": token_result["expires_in"],
                    "device_id": token_result["device_id"],
                    "user_info": {
                        "id": user.id,
                        "uuid": str(user.uuid),
                        "phone": user.phone,
                        "username": user.username,
                        "avatar_url": user.avatar_url
                    }
                }
                
            finally:
                db.close()
                
        except Exception as e:
            if not isinstance(e, (AuthenticationError, BusinessLogicError)):
                self.logger.log_operation(
                    operation="login",
                    details={
                        "phone": request.phone,
                        "error": str(e)
                    },
                    success=False
                )
            raise
    
    def _generate_verification_code(self) -> str:
        """生成6位数字验证码"""
        return ''.join(random.choices(string.digits, k=6))
    
    async def _verify_code(
        self, 
        db: Session, 
        phone: str, 
        code: str, 
        purpose: str
    ) -> bool:
        """验证验证码"""
        try:
            # 查找验证码记录
            sms_code = db.query(SMSCode).filter(
                SMSCode.phone == phone,
                SMSCode.code == code,
                SMSCode.purpose == purpose,
                SMSCode.is_used == False,
                SMSCode.is_deleted == False
            ).first()
            
            if not sms_code:
                return False
            
            # 检查是否过期
            if sms_code.is_expired():
                return False
            
            # 标记为已使用
            sms_code.mark_as_used()
            db.commit()
            
            return True
            
        except Exception as e:
            self.struct_logger.error("验证码验证失败", error=str(e))
            return False
    
    async def _send_sms(self, phone: str, code: str, purpose: str):
        """发送短信（预留接口）"""
        # 这里应该集成实际的短信服务
        # 例如：阿里云短信、腾讯云短信等
        
        self.struct_logger.info(
            "短信发送（模拟）",
            phone=phone,
            code=code if settings.is_development else "******",
            purpose=purpose
        )
        
        # 实际实现示例：
        # try:
        #     sms_client = SMSClient(
        #         access_key=settings.sms_access_key,
        #         secret_key=settings.sms_secret_key
        #     )
        #     
        #     template_id = "SMS_123456789"  # 短信模板ID
        #     template_params = {"code": code}
        #     
        #     result = await sms_client.send_sms(
        #         phone_number=phone,
        #         template_id=template_id,
        #         template_params=template_params
        #     )
        #     
        #     if not result.success:
        #         raise Exception(f"短信发送失败: {result.message}")
        #         
        # except Exception as e:
        #     self.struct_logger.error("短信发送失败", error=str(e))
        #     raise BusinessLogicError("短信发送失败，请稍后重试")
    
    async def verify_access_token(self, token: str) -> Optional[Dict[str, Any]]:
        """验证访问令牌"""
        return await self.token_service.verify_token(token)
