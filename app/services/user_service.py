"""
用户服务
处理用户信息管理相关业务逻辑
"""

from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session
import structlog

from app.models.user import User, Province, City
from app.schemas.user import UpdateUsernameRequest, UpdateAddressRequest
from app.database import get_db
from app.utils.logger import BusinessLogger
from app.core.exceptions import BusinessLogicError, ResourceNotFoundError


class UserService:
    """用户服务类"""
    
    def __init__(self):
        self.logger = BusinessLogger("user_service")
        self.struct_logger = structlog.get_logger("user_service")
    
    async def get_user_profile(self, user_id: int) -> Optional[Dict[str, Any]]:
        """获取用户资料"""
        try:
            db = next(get_db())
            try:
                # 查询用户信息
                user = db.query(User).filter(
                    User.id == user_id,
                    User.is_deleted == False
                ).first()
                if not user:
                    raise ResourceNotFoundError("用户", user_id)
                
                # 查询省市信息
                province_name = None
                city_name = None
                
                if user.province_code:
                    province = db.query(Province).filter(
                        Province.code == user.province_code,
                        Province.is_deleted == False
                    ).first()
                    if province:
                        province_name = province.name

                if user.city_code:
                    city = db.query(City).filter(
                        City.code == user.city_code,
                        City.is_deleted == False
                    ).first()
                    if city:
                        city_name = city.name
                
                # 构建用户资料
                profile = {
                    "id": user.id,
                    "uuid": str(user.uuid),
                    "phone": user.phone,
                    "username": user.username,
                    "avatar_url": user.avatar_url,
                    "province_code": user.province_code,
                    "province_name": province_name,
                    "city_code": user.city_code,
                    "city_name": city_name,
                    "is_active": user.is_active,
                    "created_at": user.created_at,
                    "last_login": user.last_login
                }
                
                self.logger.log_data_access(
                    action="read",
                    table_name="users",
                    record_count=1,
                    user_id=user_id
                )
                
                return profile
                
            finally:
                db.close()
                
        except Exception as e:
            if not isinstance(e, ResourceNotFoundError):
                self.struct_logger.error("获取用户资料失败", user_id=user_id, error=str(e))
            raise
    
    async def update_username(
        self,
        user_id: int,
        request: UpdateUsernameRequest
    ) -> Dict[str, Any]:
        """更新用户名"""
        try:
            db = next(get_db())
            try:
                # 查询用户
                user = db.query(User).filter(
                    User.id == user_id,
                    User.is_deleted == False
                ).first()
                if not user:
                    raise ResourceNotFoundError("用户", user_id)

                # 检查用户名是否已被使用
                existing_user = db.query(User).filter(
                    User.username == request.username,
                    User.id != user_id,
                    User.is_deleted == False
                ).first()
                
                if existing_user:
                    raise BusinessLogicError("用户名已被使用")
                
                # 更新用户名
                old_username = user.username
                user.username = request.username
                db.commit()
                
                self.logger.log_operation(
                    operation="update_username",
                    user_id=user_id,
                    details={
                        "old_username": old_username,
                        "new_username": request.username
                    },
                    success=True
                )
                
                return {
                    "success": True,
                    "message": "用户名更新成功",
                    "username": user.username
                }
                
            finally:
                db.close()
                
        except Exception as e:
            if not isinstance(e, (ResourceNotFoundError, BusinessLogicError)):
                self.logger.log_operation(
                    operation="update_username",
                    user_id=user_id,
                    details={"error": str(e)},
                    success=False
                )
            raise
    
    async def update_address(
        self,
        user_id: int,
        request: UpdateAddressRequest
    ) -> Dict[str, Any]:
        """更新用户地址"""
        try:
            db = next(get_db())
            try:
                # 查询用户
                user = db.query(User).filter(User.id == user_id).first()
                if not user:
                    raise ResourceNotFoundError("用户", user_id)
                
                # 验证省份代码
                province = db.query(Province).filter(
                    Province.code == request.province_code
                ).first()
                if not province:
                    raise BusinessLogicError("无效的省份代码")
                
                # 验证城市代码
                city = db.query(City).filter(
                    City.code == request.city_code,
                    City.province_code == request.province_code
                ).first()
                if not city:
                    raise BusinessLogicError("无效的城市代码")
                
                # 更新地址
                old_province = user.province_code
                old_city = user.city_code
                
                user.province_code = request.province_code
                user.city_code = request.city_code
                db.commit()
                
                self.logger.log_operation(
                    operation="update_address",
                    user_id=user_id,
                    details={
                        "old_province": old_province,
                        "old_city": old_city,
                        "new_province": request.province_code,
                        "new_city": request.city_code
                    },
                    success=True
                )
                
                return {
                    "success": True,
                    "message": "地址更新成功",
                    "province_code": user.province_code,
                    "province_name": province.name,
                    "city_code": user.city_code,
                    "city_name": city.name
                }
                
            finally:
                db.close()
                
        except Exception as e:
            if not isinstance(e, (ResourceNotFoundError, BusinessLogicError)):
                self.logger.log_operation(
                    operation="update_address",
                    user_id=user_id,
                    details={"error": str(e)},
                    success=False
                )
            raise
    
    async def update_avatar(self, user_id: int, avatar_url: str) -> Dict[str, Any]:
        """更新用户头像"""
        try:
            db = next(get_db())
            try:
                # 查询用户
                user = db.query(User).filter(User.id == user_id).first()
                if not user:
                    raise ResourceNotFoundError("用户", user_id)
                
                # 更新头像
                old_avatar = user.avatar_url
                user.avatar_url = avatar_url
                db.commit()
                
                self.logger.log_operation(
                    operation="update_avatar",
                    user_id=user_id,
                    details={
                        "old_avatar": old_avatar,
                        "new_avatar": avatar_url
                    },
                    success=True
                )
                
                return {
                    "success": True,
                    "message": "头像更新成功",
                    "avatar_url": f"{user.avatar_url}" if user.avatar_url else None
                }
                
            finally:
                db.close()
                
        except Exception as e:
            if not isinstance(e, ResourceNotFoundError):
                self.logger.log_operation(
                    operation="update_avatar",
                    user_id=user_id,
                    details={"error": str(e)},
                    success=False
                )
            raise
    
    async def get_provinces(self) -> List[Dict[str, Any]]:
        """获取省份列表"""
        try:
            db = next(get_db())
            try:
                provinces = db.query(Province).filter(
                    Province.is_deleted == False,
                    Province.is_active == True
                ).order_by(Province.code).all()
                
                self.logger.log_data_access(
                    action="read",
                    table_name="provinces",
                    record_count=len(provinces)
                )
                
                return [province.to_dict() for province in provinces]
                
            finally:
                db.close()
                
        except Exception as e:
            self.struct_logger.error("获取省份列表失败", error=str(e))
            raise
    
    async def get_cities(self, province_code: str) -> List[Dict[str, Any]]:
        """获取城市列表"""
        try:
            db = next(get_db())
            try:
                # 验证省份代码
                province = db.query(Province).filter(
                    Province.code == province_code,
                    Province.is_deleted == False
                ).first()
                if not province:
                    raise BusinessLogicError("无效的省份代码")

                # 查询城市列表
                cities = db.query(City).filter(
                    City.province_code == province_code,
                    City.is_deleted == False,
                    City.is_active == True
                ).order_by(City.code).all()
                
                self.logger.log_data_access(
                    action="read",
                    table_name="cities",
                    record_count=len(cities)
                )
                
                return [city.to_dict() for city in cities]
                
            finally:
                db.close()
                
        except Exception as e:
            if not isinstance(e, BusinessLogicError):
                self.struct_logger.error("获取城市列表失败", province_code=province_code, error=str(e))
            raise
