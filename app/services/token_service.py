"""
令牌管理服务
处理访问令牌和刷新令牌的创建、验证、刷新和撤销
"""

from typing import Optional, Dict, Any, Tu<PERSON>
from sqlalchemy.orm import Session
import structlog
import uuid

from app.models.user import User, UserToken
from app.database import get_db
from app.core.security import create_access_token, create_refresh_token, verify_token, verify_refresh_token
from app.utils.logger import BusinessLogger
from app.utils.timezone import get_expiry_time, to_utc_for_db, get_china_now
from app.core.exceptions import BusinessLogicError, ResourceNotFoundError, ValidationError


class TokenService:
    """令牌服务类"""
    
    def __init__(self):
        self.logger = BusinessLogger("token_service")
        self.struct_logger = structlog.get_logger("token_service")
    
    async def create_token_pair(
        self, 
        user: User, 
        client_ip: str = None,
        device_id: str = None,
        device_name: str = None,
        user_agent: str = None
    ) -> Dict[str, Any]:
        """
        创建访问令牌和刷新令牌对
        
        Args:
            user: 用户对象
            client_ip: 客户端IP
            device_id: 设备ID
            device_name: 设备名称
            user_agent: 用户代理
            
        Returns:
            包含令牌信息的字典
        """
        try:
            db = next(get_db())
            try:
                # 生成令牌数据
                token_data = {
                    "sub": str(user.uuid),
                    "phone": user.phone,
                    "username": user.username
                }
                
                # 创建访问令牌和刷新令牌
                access_token, access_jti = create_access_token(token_data)
                refresh_token, refresh_jti = create_refresh_token(token_data)
                
                # 计算过期时间
                access_expires_at = get_expiry_time(43200)  # 30天
                refresh_expires_at = get_expiry_time(180 * 24 * 60)  # 180天

                # 获取中国时区的当前时间用于创建时间
                from app.utils.timezone import to_utc_for_db
                current_time = to_utc_for_db()

                # 保存令牌记录到数据库
                user_token = UserToken(
                    user_id=user.id,
                    access_token_jti=access_jti,
                    refresh_token_jti=refresh_jti,
                    device_id=device_id or str(uuid.uuid4()),
                    device_name=device_name,
                    client_ip=client_ip,
                    user_agent=user_agent,
                    access_token_expires_at=access_expires_at,
                    refresh_token_expires_at=refresh_expires_at,
                    created_at=current_time,
                    updated_at=current_time,
                    is_active=True
                )
                
                db.add(user_token)
                db.commit()
                db.refresh(user_token)
                
                self.logger.log_operation(
                    operation="create_token_pair",
                    user_id=str(user.uuid),
                    details={
                        "access_jti": access_jti,
                        "refresh_jti": refresh_jti,
                        "device_id": user_token.device_id,
                        "client_ip": client_ip
                    },
                    success=True
                )
                
                return {
                    "access_token": access_token,
                    "refresh_token": refresh_token,
                    "token_type": "bearer",
                    "expires_in": 43200 * 60,  # 秒（30天）
                    "device_id": user_token.device_id,
                    "created_at": user_token.created_at.isoformat()
                }
                
            finally:
                db.close()
                
        except Exception as e:
            self.struct_logger.error("创建令牌对失败", user_id=str(user.uuid), error=str(e))
            raise BusinessLogicError("令牌创建失败")
    
    async def refresh_access_token(self, refresh_token: str) -> Dict[str, Any]:
        """
        使用刷新令牌获取新的访问令牌
        
        Args:
            refresh_token: 刷新令牌
            
        Returns:
            新的访问令牌信息
        """
        try:
            # 解码刷新令牌
            payload = verify_refresh_token(refresh_token)
            if not payload:
                raise ValidationError("无效的刷新令牌", "refresh_token", refresh_token)
            
            # 检查令牌类型
            if payload.get("type") != "refresh":
                raise ValidationError("令牌类型错误", "token_type", payload.get("type"))
            
            refresh_jti = payload.get("jti")
            user_uuid = payload.get("sub")
            
            if not refresh_jti or not user_uuid:
                raise ValidationError("令牌数据不完整", "token_data", payload)
            
            db = next(get_db())
            try:
                # 查找令牌记录
                token_record = db.query(UserToken).filter(
                    UserToken.refresh_token_jti == refresh_jti,
                    UserToken.is_active == True,
                    UserToken.is_deleted == False
                ).first()
                
                if not token_record:
                    raise ValidationError("令牌记录不存在或已失效", "refresh_jti", refresh_jti)
                
                # 检查刷新令牌是否过期
                if token_record.is_refresh_token_expired():
                    # 撤销过期的令牌
                    token_record.revoke()
                    db.commit()
                    raise ValidationError("刷新令牌已过期", "refresh_token", "expired")
                
                # 查找用户
                user = db.query(User).filter(
                    User.uuid == user_uuid,
                    User.is_deleted == False,
                    User.is_active == True
                ).first()
                
                if not user:
                    raise ResourceNotFoundError("用户", user_uuid)
                
                # 生成新的访问令牌
                token_data = {
                    "sub": str(user.uuid),
                    "phone": user.phone,
                    "username": user.username
                }
                
                new_access_token, new_access_jti = create_access_token(token_data)
                new_access_expires_at = get_expiry_time(43200)  # 30天
                
                # 更新令牌记录
                token_record.access_token_jti = new_access_jti
                token_record.access_token_expires_at = new_access_expires_at
                token_record.update_last_used()  # 这个方法内部已经更新了updated_at
                
                db.commit()
                
                self.logger.log_operation(
                    operation="refresh_access_token",
                    user_id=str(user.uuid),
                    details={
                        "old_access_jti": payload.get("jti", "unknown"),
                        "new_access_jti": new_access_jti,
                        "refresh_jti": refresh_jti
                    },
                    success=True
                )
                
                return {
                    "access_token": new_access_token,
                    "token_type": "bearer",
                    "expires_in": 43200 * 60,  # 秒（30天）
                    "refreshed_at": get_china_now().isoformat()
                }
                
            finally:
                db.close()
                
        except (ValidationError, ResourceNotFoundError):
            raise
        except Exception as e:
            self.struct_logger.error("刷新访问令牌失败", error=str(e))
            raise BusinessLogicError("令牌刷新失败")
    
    async def revoke_token(self, access_token: str) -> bool:
        """
        撤销令牌（登出）
        
        Args:
            access_token: 访问令牌
            
        Returns:
            是否成功撤销
        """
        try:
            # 解码访问令牌
            payload = verify_token(access_token)
            if not payload:
                return False
            
            access_jti = payload.get("jti")
            user_uuid = payload.get("sub")
            
            if not access_jti:
                return False
            
            db = next(get_db())
            try:
                # 查找令牌记录
                token_record = db.query(UserToken).filter(
                    UserToken.access_token_jti == access_jti,
                    UserToken.is_active == True,
                    UserToken.is_deleted == False
                ).first()
                
                if token_record:
                    # 撤销令牌（revoke方法内部已经更新了updated_at）
                    token_record.revoke()
                    db.commit()
                    
                    self.logger.log_operation(
                        operation="revoke_token",
                        user_id=user_uuid,
                        details={
                            "access_jti": access_jti,
                            "refresh_jti": token_record.refresh_token_jti
                        },
                        success=True
                    )
                    
                    return True
                
                return False
                
            finally:
                db.close()
                
        except Exception as e:
            self.struct_logger.error("撤销令牌失败", error=str(e))
            return False
    
    async def revoke_all_user_tokens(self, user_uuid: str) -> int:
        """
        撤销用户的所有令牌
        
        Args:
            user_uuid: 用户UUID
            
        Returns:
            撤销的令牌数量
        """
        try:
            db = next(get_db())
            try:
                # 查找用户
                user = db.query(User).filter(
                    User.uuid == user_uuid,
                    User.is_deleted == False
                ).first()
                
                if not user:
                    return 0
                
                # 撤销所有活跃的令牌
                active_tokens = db.query(UserToken).filter(
                    UserToken.user_id == user.id,
                    UserToken.is_active == True,
                    UserToken.is_deleted == False
                ).all()
                
                revoked_count = 0
                for token in active_tokens:
                    token.revoke()
                    revoked_count += 1
                
                db.commit()
                
                self.logger.log_operation(
                    operation="revoke_all_user_tokens",
                    user_id=user_uuid,
                    details={"revoked_count": revoked_count},
                    success=True
                )
                
                return revoked_count
                
            finally:
                db.close()
                
        except Exception as e:
            self.struct_logger.error("撤销用户所有令牌失败", user_uuid=user_uuid, error=str(e))
            return 0
    
    async def verify_token(self, access_token: str) -> Optional[Dict[str, Any]]:
        """
        验证访问令牌
        
        Args:
            access_token: 访问令牌
            
        Returns:
            用户信息或 None
        """
        try:
            # 解码令牌
            payload = verify_token(access_token)
            if not payload:
                return None
            
            access_jti = payload.get("jti")
            user_uuid = payload.get("sub")
            
            if not access_jti or not user_uuid:
                return None
            
            db = next(get_db())
            try:
                # 查找令牌记录
                token_record = db.query(UserToken).filter(
                    UserToken.access_token_jti == access_jti,
                    UserToken.is_active == True,
                    UserToken.is_deleted == False
                ).first()
                
                if not token_record:
                    return None
                
                # 检查访问令牌是否过期
                if token_record.is_access_token_expired():
                    return None
                
                # 查找用户
                user = db.query(User).filter(
                    User.uuid == user_uuid,
                    User.is_deleted == False,
                    User.is_active == True
                ).first()
                
                if not user:
                    return None
                
                # 更新最后使用时间
                token_record.update_last_used()
                db.commit()
                
                return {
                    "user_id": user.id,  # 整数ID，用于数据库查询
                    "user_uuid": str(user.uuid),  # UUID字符串，用于对外接口
                    "phone": user.phone,
                    "username": user.username,
                    "is_active": user.is_active,
                    "device_id": token_record.device_id
                }
                
            finally:
                db.close()
                
        except Exception as e:
            self.struct_logger.error("验证令牌失败", error=str(e))
            return None
