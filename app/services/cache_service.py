"""
缓存服务
提供对话历史和相关数据的缓存机制
"""

import json
import hashlib
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import structlog

logger = structlog.get_logger("cache_service")


class MemoryCache:
    """内存缓存实现"""
    
    def __init__(self, default_ttl: int = 300):  # 默认5分钟过期
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.default_ttl = default_ttl
    
    def _is_expired(self, cache_item: Dict[str, Any]) -> bool:
        """检查缓存项是否过期"""
        if 'expires_at' not in cache_item:
            return True
        return datetime.now() > cache_item['expires_at']
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if key not in self.cache:
            return None
        
        cache_item = self.cache[key]
        if self._is_expired(cache_item):
            del self.cache[key]
            return None
        
        # 更新访问时间
        cache_item['last_accessed'] = datetime.now()
        return cache_item['value']
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        if ttl is None:
            ttl = self.default_ttl
        
        expires_at = datetime.now() + timedelta(seconds=ttl)
        self.cache[key] = {
            'value': value,
            'expires_at': expires_at,
            'created_at': datetime.now(),
            'last_accessed': datetime.now()
        }
    
    def delete(self, key: str) -> bool:
        """删除缓存项"""
        if key in self.cache:
            del self.cache[key]
            return True
        return False
    
    def clear(self) -> None:
        """清空所有缓存"""
        self.cache.clear()
    
    def cleanup_expired(self) -> int:
        """清理过期缓存项"""
        expired_keys = []
        for key, cache_item in self.cache.items():
            if self._is_expired(cache_item):
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.cache[key]
        
        return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_items = len(self.cache)
        expired_count = sum(1 for item in self.cache.values() if self._is_expired(item))
        
        return {
            'total_items': total_items,
            'active_items': total_items - expired_count,
            'expired_items': expired_count,
            'memory_usage_estimate': sum(len(str(item)) for item in self.cache.values())
        }


class ConversationCacheService:
    """对话缓存服务"""
    
    def __init__(self):
        self.cache = MemoryCache(default_ttl=600)  # 对话历史缓存10分钟
        self.struct_logger = structlog.get_logger("conversation_cache")
    
    def _generate_cache_key(self, conversation_id: str, user_id: int, **kwargs) -> str:
        """生成缓存键"""
        # 将参数序列化并生成哈希
        key_data = {
            'conversation_id': conversation_id,
            'user_id': user_id,
            **kwargs
        }
        key_string = json.dumps(key_data, sort_keys=True)
        return f"conv_history:{hashlib.md5(key_string.encode()).hexdigest()}"
    
    def get_conversation_history(self, conversation_id: str, user_id: int, 
                               page: int = 1, limit: int = 50) -> Optional[List[Dict[str, Any]]]:
        """获取缓存的对话历史"""
        cache_key = self._generate_cache_key(
            conversation_id=conversation_id,
            user_id=user_id,
            page=page,
            limit=limit
        )
        
        cached_data = self.cache.get(cache_key)
        if cached_data:
            self.struct_logger.info(
                "对话历史缓存命中",
                conversation_id=conversation_id,
                user_id=user_id,
                cache_key=cache_key
            )
            return cached_data
        
        return None
    
    def set_conversation_history(self, conversation_id: str, user_id: int,
                               history_data: List[Dict[str, Any]], 
                               page: int = 1, limit: int = 50, ttl: int = 600) -> None:
        """缓存对话历史"""
        cache_key = self._generate_cache_key(
            conversation_id=conversation_id,
            user_id=user_id,
            page=page,
            limit=limit
        )
        
        self.cache.set(cache_key, history_data, ttl)
        
        self.struct_logger.info(
            "对话历史已缓存",
            conversation_id=conversation_id,
            user_id=user_id,
            cache_key=cache_key,
            data_size=len(history_data)
        )
    
    def invalidate_conversation(self, conversation_id: str, user_id: Optional[int] = None) -> int:
        """使对话相关的缓存失效"""
        invalidated_count = 0
        
        # 查找所有相关的缓存键
        keys_to_delete = []
        for key in self.cache.cache.keys():
            if key.startswith("conv_history:"):
                # 尝试从缓存中获取数据来检查是否匹配
                cached_item = self.cache.cache[key]
                if 'value' in cached_item:
                    # 这里简化处理，实际可以存储更多元数据来精确匹配
                    if conversation_id in key:
                        keys_to_delete.append(key)
        
        # 删除匹配的缓存项
        for key in keys_to_delete:
            if self.cache.delete(key):
                invalidated_count += 1
        
        self.struct_logger.info(
            "对话缓存已失效",
            conversation_id=conversation_id,
            user_id=user_id,
            invalidated_count=invalidated_count
        )
        
        return invalidated_count
    
    def get_recommendation_cache(self, message_id: str) -> Optional[List[Dict[str, Any]]]:
        """获取推荐数据缓存"""
        cache_key = f"recommendations:{message_id}"
        return self.cache.get(cache_key)
    
    def set_recommendation_cache(self, message_id: str, recommendations: List[Dict[str, Any]], 
                               ttl: int = 1800) -> None:
        """缓存推荐数据"""
        cache_key = f"recommendations:{message_id}"
        self.cache.set(cache_key, recommendations, ttl)
        
        self.struct_logger.info(
            "推荐数据已缓存",
            message_id=message_id,
            cache_key=cache_key,
            data_size=len(recommendations)
        )
    
    def get_comparison_cache(self, message_id: str) -> Optional[List[Dict[str, Any]]]:
        """获取对比数据缓存"""
        cache_key = f"comparisons:{message_id}"
        return self.cache.get(cache_key)
    
    def set_comparison_cache(self, message_id: str, comparisons: List[Dict[str, Any]], 
                           ttl: int = 1800) -> None:
        """缓存对比数据"""
        cache_key = f"comparisons:{message_id}"
        self.cache.set(cache_key, comparisons, ttl)
        
        self.struct_logger.info(
            "对比数据已缓存",
            message_id=message_id,
            cache_key=cache_key,
            data_size=len(comparisons)
        )
    
    def cleanup_expired_cache(self) -> Dict[str, int]:
        """清理过期缓存"""
        expired_count = self.cache.cleanup_expired()
        
        self.struct_logger.info(
            "缓存清理完成",
            expired_count=expired_count
        )
        
        return {
            'expired_count': expired_count,
            'stats': self.cache.get_stats()
        }
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return self.cache.get_stats()


# 全局缓存服务实例
conversation_cache = ConversationCacheService()
