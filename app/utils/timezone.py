"""
时区工具模块
统一管理时区相关功能，确保所有时间都使用中国时区（UTC+8）
"""

from datetime import datetime, timezone, timedelta
from typing import Optional
import pytz

# 中国时区
CHINA_TZ = pytz.timezone('Asia/Shanghai')
UTC_TZ = pytz.UTC


def get_china_now() -> datetime:
    """
    获取当前中国时间
    返回带时区信息的 datetime 对象
    """
    return datetime.now(CHINA_TZ)


def get_china_utc_now() -> datetime:
    """
    获取当前中国时间对应的 UTC 时间
    用于存储到数据库（数据库字段为 timezone-aware）
    """
    china_time = get_china_now()
    return china_time.astimezone(UTC_TZ)


def to_china_time(dt: datetime) -> datetime:
    """
    将任意时区的时间转换为中国时区
    
    Args:
        dt: 输入的 datetime 对象
        
    Returns:
        中国时区的 datetime 对象
    """
    if dt is None:
        return None
    
    # 如果没有时区信息，假设是 UTC
    if dt.tzinfo is None:
        dt = UTC_TZ.localize(dt)
    
    # 转换为中国时区
    return dt.astimezone(CHINA_TZ)


def to_utc_for_db(dt: Optional[datetime] = None) -> datetime:
    """
    获取用于数据库存储的 UTC 时间
    
    Args:
        dt: 可选的 datetime 对象，如果为 None 则使用当前时间
        
    Returns:
        UTC 时区的 datetime 对象，用于数据库存储
    """
    if dt is None:
        # 获取当前中国时间
        china_time = get_china_now()
    else:
        # 转换输入时间为中国时区
        china_time = to_china_time(dt)
    
    # 转换为 UTC 用于数据库存储
    return china_time.astimezone(UTC_TZ)


def format_china_time(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """
    格式化中国时区时间为字符串
    
    Args:
        dt: datetime 对象
        format_str: 格式化字符串
        
    Returns:
        格式化后的时间字符串
    """
    if dt is None:
        return ""
    
    china_time = to_china_time(dt)
    return china_time.strftime(format_str)


def parse_china_time(time_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> datetime:
    """
    解析时间字符串为中国时区的 datetime 对象
    
    Args:
        time_str: 时间字符串
        format_str: 解析格式
        
    Returns:
        中国时区的 datetime 对象
    """
    naive_dt = datetime.strptime(time_str, format_str)
    return CHINA_TZ.localize(naive_dt)


def get_china_date_range(date_str: str) -> tuple[datetime, datetime]:
    """
    获取指定日期在中国时区的开始和结束时间
    
    Args:
        date_str: 日期字符串，格式为 YYYY-MM-DD
        
    Returns:
        (开始时间, 结束时间) 的元组，都是 UTC 时间用于数据库查询
    """
    try:
        # 解析日期
        date_obj = datetime.strptime(date_str, "%Y-%m-%d").date()
        
        # 中国时区的开始时间（00:00:00）
        start_china = CHINA_TZ.localize(datetime.combine(date_obj, datetime.min.time()))
        
        # 中国时区的结束时间（23:59:59.999999）
        end_china = CHINA_TZ.localize(datetime.combine(date_obj, datetime.max.time()))
        
        # 转换为 UTC 用于数据库查询
        start_utc = start_china.astimezone(UTC_TZ)
        end_utc = end_china.astimezone(UTC_TZ)
        
        return start_utc, end_utc
        
    except ValueError as e:
        raise ValueError(f"无效的日期格式: {date_str}，应为 YYYY-MM-DD") from e


def get_expiry_time(minutes: int) -> datetime:
    """
    获取从当前中国时间开始的过期时间
    
    Args:
        minutes: 过期分钟数
        
    Returns:
        UTC 时区的过期时间，用于数据库存储
    """
    china_now = get_china_now()
    china_expiry = china_now + timedelta(minutes=minutes)
    return china_expiry.astimezone(UTC_TZ)


def is_expired(expiry_time: datetime) -> bool:
    """
    检查时间是否已过期（基于中国时区）
    
    Args:
        expiry_time: 过期时间（UTC 或任意时区）
        
    Returns:
        是否已过期
    """
    if expiry_time is None:
        return True
    
    # 转换为中国时区进行比较
    china_expiry = to_china_time(expiry_time)
    china_now = get_china_now()
    
    return china_now > china_expiry


def get_china_timestamp() -> float:
    """
    获取中国时区的时间戳
    
    Returns:
        时间戳（秒）
    """
    return get_china_now().timestamp()


def from_timestamp_to_china(timestamp: float) -> datetime:
    """
    从时间戳转换为中国时区时间
    
    Args:
        timestamp: 时间戳（秒）
        
    Returns:
        中国时区的 datetime 对象
    """
    utc_time = datetime.fromtimestamp(timestamp, tz=UTC_TZ)
    return utc_time.astimezone(CHINA_TZ)


# 常用的时间格式
CHINA_DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"
CHINA_DATE_FORMAT = "%Y-%m-%d"
CHINA_TIME_FORMAT = "%H:%M:%S"
ISO_FORMAT = "%Y-%m-%dT%H:%M:%S%z"
