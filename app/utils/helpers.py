"""
辅助函数和工具类
提供通用的工具函数和辅助类
"""

import re
import hashlib
import secrets
import string
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta
from decimal import Decimal
import uuid
import json
from pathlib import Path
import structlog

logger = structlog.get_logger("helpers")


class DateTimeHelper:
    """日期时间辅助类"""
    
    @staticmethod
    def now() -> datetime:
        """获取当前UTC时间"""
        return datetime.utcnow()
    
    @staticmethod
    def format_datetime(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
        """格式化日期时间"""
        return dt.strftime(format_str)
    
    @staticmethod
    def parse_datetime(date_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> datetime:
        """解析日期时间字符串"""
        return datetime.strptime(date_str, format_str)
    
    @staticmethod
    def add_days(dt: datetime, days: int) -> datetime:
        """添加天数"""
        return dt + timedelta(days=days)
    
    @staticmethod
    def add_hours(dt: datetime, hours: int) -> datetime:
        """添加小时"""
        return dt + timedelta(hours=hours)
    
    @staticmethod
    def add_minutes(dt: datetime, minutes: int) -> datetime:
        """添加分钟"""
        return dt + timedelta(minutes=minutes)
    
    @staticmethod
    def is_expired(dt: datetime) -> bool:
        """检查是否过期"""
        return datetime.utcnow() > dt
    
    @staticmethod
    def time_until_expiry(dt: datetime) -> timedelta:
        """计算到期剩余时间"""
        return dt - datetime.utcnow()
    
    @staticmethod
    def to_timestamp(dt: datetime) -> float:
        """转换为时间戳"""
        return dt.timestamp()
    
    @staticmethod
    def from_timestamp(timestamp: float) -> datetime:
        """从时间戳创建日期时间"""
        return datetime.fromtimestamp(timestamp)


class StringHelper:
    """字符串辅助类"""
    
    @staticmethod
    def generate_random_string(length: int = 32, include_symbols: bool = False) -> str:
        """生成随机字符串"""
        characters = string.ascii_letters + string.digits
        if include_symbols:
            characters += "!@#$%^&*"
        
        return ''.join(secrets.choice(characters) for _ in range(length))
    
    @staticmethod
    def generate_uuid() -> str:
        """生成UUID字符串"""
        return str(uuid.uuid4())
    
    @staticmethod
    def generate_short_id(length: int = 8) -> str:
        """生成短ID"""
        return secrets.token_urlsafe(length)[:length]
    
    @staticmethod
    def slugify(text: str) -> str:
        """将文本转换为URL友好的slug"""
        # 转换为小写
        text = text.lower()
        # 替换空格和特殊字符为连字符
        text = re.sub(r'[^\w\s-]', '', text)
        text = re.sub(r'[-\s]+', '-', text)
        # 去除首尾连字符
        return text.strip('-')
    
    @staticmethod
    def truncate(text: str, max_length: int, suffix: str = "...") -> str:
        """截断文本"""
        if len(text) <= max_length:
            return text
        
        return text[:max_length - len(suffix)] + suffix
    
    @staticmethod
    def mask_email(email: str) -> str:
        """掩码邮箱地址"""
        if '@' not in email:
            return email
        
        local, domain = email.split('@', 1)
        if len(local) <= 2:
            masked_local = local
        else:
            masked_local = local[0] + '*' * (len(local) - 2) + local[-1]
        
        return f"{masked_local}@{domain}"
    
    @staticmethod
    def mask_phone(phone: str) -> str:
        """掩码手机号"""
        if len(phone) < 7:
            return phone
        
        return phone[:3] + '*' * (len(phone) - 6) + phone[-3:]
    
    @staticmethod
    def is_valid_email(email: str) -> bool:
        """验证邮箱格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    @staticmethod
    def is_valid_phone(phone: str) -> bool:
        """验证手机号格式（中国大陆）"""
        pattern = r'^1[3-9]\d{9}$'
        return bool(re.match(pattern, phone))
    
    @staticmethod
    def clean_html(text: str) -> str:
        """清理HTML标签"""
        import re
        clean = re.compile('<.*?>')
        return re.sub(clean, '', text)


class HashHelper:
    """哈希辅助类"""
    
    @staticmethod
    def md5(text: str) -> str:
        """计算MD5哈希"""
        return hashlib.md5(text.encode()).hexdigest()
    
    @staticmethod
    def sha256(text: str) -> str:
        """计算SHA256哈希"""
        return hashlib.sha256(text.encode()).hexdigest()
    
    @staticmethod
    def sha1(text: str) -> str:
        """计算SHA1哈希"""
        return hashlib.sha1(text.encode()).hexdigest()
    
    @staticmethod
    def generate_salt(length: int = 32) -> str:
        """生成盐值"""
        return secrets.token_hex(length)
    
    @staticmethod
    def hash_with_salt(text: str, salt: str) -> str:
        """使用盐值计算哈希"""
        return hashlib.sha256((text + salt).encode()).hexdigest()


class NumberHelper:
    """数字辅助类"""
    
    @staticmethod
    def format_currency(amount: Union[int, float, Decimal], currency: str = "¥") -> str:
        """格式化货币"""
        return f"{currency}{amount:,.2f}"
    
    @staticmethod
    def format_percentage(value: float, decimal_places: int = 2) -> str:
        """格式化百分比"""
        return f"{value:.{decimal_places}f}%"
    
    @staticmethod
    def round_decimal(value: Union[float, Decimal], places: int = 2) -> Decimal:
        """四舍五入到指定小数位"""
        if isinstance(value, float):
            value = Decimal(str(value))
        
        return value.quantize(Decimal('0.' + '0' * places))
    
    @staticmethod
    def clamp(value: Union[int, float], min_val: Union[int, float], max_val: Union[int, float]) -> Union[int, float]:
        """限制数值在指定范围内"""
        return max(min_val, min(value, max_val))
    
    @staticmethod
    def is_positive(value: Union[int, float]) -> bool:
        """检查是否为正数"""
        return value > 0
    
    @staticmethod
    def is_negative(value: Union[int, float]) -> bool:
        """检查是否为负数"""
        return value < 0


class FileHelper:
    """文件辅助类"""
    
    @staticmethod
    def get_file_extension(filename: str) -> str:
        """获取文件扩展名"""
        return Path(filename).suffix.lower()
    
    @staticmethod
    def get_file_name_without_extension(filename: str) -> str:
        """获取不带扩展名的文件名"""
        return Path(filename).stem
    
    @staticmethod
    def is_image_file(filename: str) -> bool:
        """检查是否为图片文件"""
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'}
        return FileHelper.get_file_extension(filename) in image_extensions
    
    @staticmethod
    def is_document_file(filename: str) -> bool:
        """检查是否为文档文件"""
        doc_extensions = {'.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt'}
        return FileHelper.get_file_extension(filename) in doc_extensions
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f}{size_names[i]}"
    
    @staticmethod
    def generate_unique_filename(original_filename: str) -> str:
        """生成唯一文件名"""
        name = FileHelper.get_file_name_without_extension(original_filename)
        extension = FileHelper.get_file_extension(original_filename)
        timestamp = int(datetime.utcnow().timestamp())
        random_str = StringHelper.generate_short_id(6)
        
        return f"{name}_{timestamp}_{random_str}{extension}"


class ValidationHelper:
    """验证辅助类"""
    
    @staticmethod
    def is_valid_url(url: str) -> bool:
        """验证URL格式"""
        pattern = r'^https?://(?:[-\w.])+(?:\:[0-9]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?$'
        return bool(re.match(pattern, url))
    
    @staticmethod
    def is_valid_ipv4(ip: str) -> bool:
        """验证IPv4地址"""
        pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
        return bool(re.match(pattern, ip))
    
    @staticmethod
    def is_strong_password(password: str) -> bool:
        """检查密码强度"""
        if len(password) < 8:
            return False
        
        has_upper = bool(re.search(r'[A-Z]', password))
        has_lower = bool(re.search(r'[a-z]', password))
        has_digit = bool(re.search(r'\d', password))
        has_special = bool(re.search(r'[!@#$%^&*(),.?":{}|<>]', password))
        
        return has_upper and has_lower and has_digit and has_special
    
    @staticmethod
    def is_valid_json(json_str: str) -> bool:
        """验证JSON格式"""
        try:
            json.loads(json_str)
            return True
        except (ValueError, TypeError):
            return False


class ListHelper:
    """列表辅助类"""
    
    @staticmethod
    def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
        """将列表分块"""
        return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]
    
    @staticmethod
    def flatten_list(nested_list: List[List[Any]]) -> List[Any]:
        """展平嵌套列表"""
        return [item for sublist in nested_list for item in sublist]
    
    @staticmethod
    def remove_duplicates(lst: List[Any]) -> List[Any]:
        """去除重复项（保持顺序）"""
        seen = set()
        result = []
        for item in lst:
            if item not in seen:
                seen.add(item)
                result.append(item)
        return result
    
    @staticmethod
    def safe_get(lst: List[Any], index: int, default: Any = None) -> Any:
        """安全获取列表元素"""
        try:
            return lst[index]
        except (IndexError, TypeError):
            return default


class DictHelper:
    """字典辅助类"""
    
    @staticmethod
    def safe_get(d: Dict[str, Any], key: str, default: Any = None) -> Any:
        """安全获取字典值"""
        return d.get(key, default)
    
    @staticmethod
    def deep_merge(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
        """深度合并字典"""
        result = dict1.copy()
        
        for key, value in dict2.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = DictHelper.deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    @staticmethod
    def filter_none_values(d: Dict[str, Any]) -> Dict[str, Any]:
        """过滤None值"""
        return {k: v for k, v in d.items() if v is not None}
    
    @staticmethod
    def flatten_dict(d: Dict[str, Any], separator: str = '.') -> Dict[str, Any]:
        """展平嵌套字典"""
        def _flatten(obj, parent_key=''):
            items = []
            for k, v in obj.items():
                new_key = f"{parent_key}{separator}{k}" if parent_key else k
                if isinstance(v, dict):
                    items.extend(_flatten(v, new_key).items())
                else:
                    items.append((new_key, v))
            return dict(items)
        
        return _flatten(d)
