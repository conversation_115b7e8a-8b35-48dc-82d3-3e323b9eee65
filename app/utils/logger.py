"""
日志工具类
提供便捷的日志记录方法和上下文管理
"""

import time
import uuid
from typing import Any, Dict, Optional, Union
from contextvars import ContextVar
import structlog
from fastapi import Request, Response
from app.config import settings
from app.utils.timezone import get_china_timestamp

# 请求上下文变量
request_id_var: ContextVar[str] = ContextVar('request_id', default='')
user_id_var: ContextVar[str] = ContextVar('user_id', default='')


class LoggerMixin:
    """日志混入类，为其他类提供日志功能"""
    
    def __init__(self):
        self.logger = structlog.get_logger(self.__class__.__name__)
    
    def log_info(self, message: str, **kwargs):
        """记录信息日志"""
        self.logger.info(message, **kwargs)
    
    def log_warning(self, message: str, **kwargs):
        """记录警告日志"""
        self.logger.warning(message, **kwargs)
    
    def log_error(self, message: str, **kwargs):
        """记录错误日志"""
        self.logger.error(message, **kwargs)
    
    def log_debug(self, message: str, **kwargs):
        """记录调试日志"""
        self.logger.debug(message, **kwargs)


class RequestLogger:
    """请求日志记录器"""
    
    def __init__(self):
        self.logger = structlog.get_logger("request")
    
    def log_request_start(self, request: Request, request_id: str) -> float:
        """记录请求开始"""
        start_time = get_china_timestamp()
        
        # 设置请求上下文
        request_id_var.set(request_id)
        
        # 提取客户端信息
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get("user-agent", "")
        
        self.logger.info(
            "请求开始",
            request_id=request_id,
            method=request.method,
            url=str(request.url),
            path=request.url.path,
            query_params=dict(request.query_params),
            client_ip=client_ip,
            user_agent=user_agent,
            headers=dict(request.headers) if settings.debug else {},
            timestamp=start_time
        )
        
        return start_time
    
    def log_request_end(
        self, 
        request: Request, 
        response: Response, 
        start_time: float,
        request_id: str,
        error: Optional[Exception] = None
    ):
        """记录请求结束"""
        end_time = get_china_timestamp()
        duration = end_time - start_time
        
        log_data = {
            "request_id": request_id,
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "status_code": response.status_code,
            "duration_ms": round(duration * 1000, 2),
            "response_size": response.headers.get("content-length", "unknown"),
            "timestamp": end_time
        }
        
        if error:
            log_data.update({
                "error": str(error),
                "error_type": type(error).__name__
            })
            self.logger.error("请求处理失败", **log_data)
        else:
            # 根据状态码确定日志级别
            if response.status_code >= 500:
                self.logger.error("请求完成（服务器错误）", **log_data)
            elif response.status_code >= 400:
                self.logger.warning("请求完成（客户端错误）", **log_data)
            else:
                self.logger.info("请求完成", **log_data)
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端真实IP"""
        # 检查代理头
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # 返回直接连接的IP
        if request.client:
            return request.client.host
        
        return "unknown"


class BusinessLogger:
    """业务日志记录器"""
    
    def __init__(self, module_name: str):
        self.logger = structlog.get_logger(f"business.{module_name}")
        self.module_name = module_name
    
    def log_operation(
        self, 
        operation: str, 
        user_id: Optional[str] = None,
        resource_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        success: bool = True
    ):
        """记录业务操作"""
        log_data = {
            "operation": operation,
            "module": self.module_name,
            "request_id": request_id_var.get(),
            "user_id": user_id or user_id_var.get(),
            "success": success,
            "timestamp": get_china_timestamp()
        }
        
        if resource_id:
            log_data["resource_id"] = resource_id
        
        if details:
            log_data["details"] = details
        
        if success:
            self.logger.info("业务操作执行", **log_data)
        else:
            self.logger.error("业务操作失败", **log_data)
    
    def log_data_access(
        self, 
        action: str, 
        table_name: str,
        record_count: int = 1,
        user_id: Optional[str] = None
    ):
        """记录数据访问"""
        self.logger.info(
            "数据访问",
            action=action,
            table_name=table_name,
            record_count=record_count,
            user_id=user_id or user_id_var.get(),
            request_id=request_id_var.get(),
            module=self.module_name,
            timestamp=get_china_timestamp()
        )


class SecurityLogger:
    """安全日志记录器"""
    
    def __init__(self):
        self.logger = structlog.get_logger("security")
    
    def log_authentication(
        self, 
        user_id: str, 
        success: bool, 
        method: str = "password",
        client_ip: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        """记录认证事件"""
        log_data = {
            "event_type": "authentication",
            "user_id": user_id,
            "success": success,
            "method": method,
            "client_ip": client_ip,
            "request_id": request_id_var.get(),
            "timestamp": get_china_timestamp()
        }
        
        if details:
            log_data["details"] = details
        
        if success:
            self.logger.info("用户认证成功", **log_data)
        else:
            self.logger.warning("用户认证失败", **log_data)
    
    def log_authorization(
        self, 
        user_id: str, 
        resource: str, 
        action: str,
        success: bool,
        client_ip: Optional[str] = None
    ):
        """记录授权事件"""
        log_data = {
            "event_type": "authorization",
            "user_id": user_id,
            "resource": resource,
            "action": action,
            "success": success,
            "client_ip": client_ip,
            "request_id": request_id_var.get(),
            "timestamp": get_china_timestamp()
        }
        
        if success:
            self.logger.info("授权检查通过", **log_data)
        else:
            self.logger.warning("授权检查失败", **log_data)
    
    def log_suspicious_activity(
        self, 
        activity_type: str, 
        description: str,
        user_id: Optional[str] = None,
        client_ip: Optional[str] = None,
        severity: str = "medium"
    ):
        """记录可疑活动"""
        self.logger.warning(
            "检测到可疑活动",
            activity_type=activity_type,
            description=description,
            user_id=user_id,
            client_ip=client_ip,
            severity=severity,
            request_id=request_id_var.get(),
            timestamp=get_china_timestamp()
        )


def generate_request_id() -> str:
    """生成请求ID"""
    return str(uuid.uuid4())


def set_user_context(user_id: str):
    """设置用户上下文"""
    user_id_var.set(user_id)


def get_request_id() -> str:
    """获取当前请求ID"""
    return request_id_var.get()


def get_user_id() -> str:
    """获取当前用户ID"""
    return user_id_var.get()
