"""
TTS（文本转语音）相关的 API 端点
"""

import json
import httpx
import uuid
from typing import Dict, Any
from fastapi import APIRouter, Depends, Request, HTTPException
import structlog

from app.schemas.tts import TTSRequest, TTSResponse
from app.schemas.response import create_success_response, SuccessResponse
from app.dependencies import get_current_request_id, require_authentication
from app.utils.logger import BusinessLogger

router = APIRouter()
logger = BusinessLogger("tts_api")
struct_logger = structlog.get_logger("tts_api")


@router.post(
    "/",
    response_model=SuccessResponse[TTSResponse],
    tags=["TTS服务"],
    summary="文本转语音",
    description="将文本内容转换为语音文件"
)
async def text_to_speech(
    request_data: TTSRequest,
    current_user: Dict[str, Any] = Depends(require_authentication),
    request_id: str = Depends(get_current_request_id)
):
    """
    文本转语音接口
    
    接收文本内容，调用字节跳动TTS服务进行语音合成，返回语音文件。
    
    Args:
        request_data: TTS请求数据，包含要转换的文本
        current_user: 当前认证用户信息
        request_id: 请求ID，用于日志追踪
    
    Returns:
        TTSResponse: 包含TTS服务返回的原始数据
    
    Raises:
        HTTPException: 当文本过长、TTS服务调用失败时抛出
    """
    try:
        authenticated_user_id = current_user.get("user_id")
        
        struct_logger.info(
            "开始处理TTS请求",
            user_id=authenticated_user_id,
            message_length=len(request_data.message),
            request_id=request_id
        )
        
        # 验证文本长度
        if len(request_data.message) > 5000:
            raise HTTPException(
                status_code=400,
                detail="文本内容过长，最大支持5000个字符"
            )
        
        # 构建外部API请求
        external_api_url = "https://openspeech.bytedance.com/api/v1/tts"
        
        # 生成唯一的请求ID
        tts_request_id = f"tts_{int(uuid.uuid4().time_low)}_{uuid.uuid4().hex[:8]}"
        
        # 构建请求体
        request_payload = {
            "app": {
                "appid": "4421837519",
                "token": "IGyXC33I6azxMQilBJwblLsl-bzpEpsb",
                "cluster": "volcano_tts"
            },
            "user": {
                "uid": "user_1751515380912_kp5ugbpb4"
            },
            "audio": {
                "voice_type": "zh_female_roumeinvyou_emo_v2_mars_bigtts",
                "encoding": "mp3",
                "speed_ratio": 1
            },
            "request": {
                "reqid": tts_request_id,
                "text": request_data.message,
                "operation": "query"
            }
        }
        
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer;IGyXC33I6azxMQilBJwblLsl-bzpEpsb"
        }
        
        struct_logger.info(
            "开始调用TTS外部API",
            external_url=external_api_url,
            request_id=tts_request_id,
            user_id=authenticated_user_id,
            text_preview=request_data.message[:50] + "..." if len(request_data.message) > 50 else request_data.message,
            request_id_trace=request_id
        )
        
        try:
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    external_api_url,
                    headers=headers,
                    json=request_payload
                )
                
                struct_logger.info(
                    "TTS外部API调用完成",
                    status_code=response.status_code,
                    user_id=authenticated_user_id,
                    request_id=tts_request_id,
                    request_id_trace=request_id
                )
                
                # 检查响应状态
                if response.status_code != 200:
                    struct_logger.error(
                        "TTS外部API调用失败",
                        status_code=response.status_code,
                        response_text=response.text,
                        user_id=authenticated_user_id,
                        request_id=tts_request_id,
                        request_id_trace=request_id
                    )
                    raise HTTPException(
                        status_code=response.status_code,
                        detail=f"TTS服务调用失败: {response.text}"
                    )
                
                # 尝试解析JSON响应，如果失败则返回原始文本
                try:
                    response_data = response.json()
                except json.JSONDecodeError:
                    response_data = response.text
                
                struct_logger.info(
                    "TTS处理成功",
                    user_id=authenticated_user_id,
                    request_id=tts_request_id,
                    response_size=len(str(response_data)),
                    request_id_trace=request_id
                )
                
                # 原样返回外部API响应
                return create_success_response(
                    data=TTSResponse(data=response_data),
                    message="文本转语音成功",
                    request_id=request_id
                )
                
        except httpx.TimeoutException:
            struct_logger.error(
                "TTS外部API调用超时",
                user_id=authenticated_user_id,
                request_id=tts_request_id,
                request_id_trace=request_id
            )
            raise HTTPException(
                status_code=504,
                detail="TTS服务调用超时"
            )
        except httpx.RequestError as e:
            struct_logger.error(
                "TTS外部API调用网络错误",
                error=str(e),
                user_id=authenticated_user_id,
                request_id=tts_request_id,
                request_id_trace=request_id
            )
            raise HTTPException(
                status_code=502,
                detail="TTS服务连接失败"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        struct_logger.error(
            "TTS处理失败",
            error=str(e),
            user_id=authenticated_user_id,
            message_preview=request_data.message[:50] + "..." if len(request_data.message) > 50 else request_data.message,
            request_id=request_id
        )
        raise HTTPException(
            status_code=500,
            detail="TTS服务异常"
        )
