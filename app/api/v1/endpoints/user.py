"""
用户信息管理的 API 端点
"""

from typing import Dict, Any, List
from fastapi import APIRouter, Depends, UploadFile, File, HTTPException
from fastapi.responses import FileResponse
import structlog
import os

from app.schemas.user import (
    UserProfile, UpdateUsernameRequest, UpdateAddressRequest, 
    UploadAvatarResponse, Province, City
)
from app.schemas.response import create_success_response
from app.services.user_service import UserService
from app.services.file_service import FileService
from app.dependencies import require_authentication, get_current_request_id
from app.utils.logger import BusinessLogger

router = APIRouter()
user_service = UserService()
file_service = FileService()
logger = BusinessLogger("user_api")
struct_logger = structlog.get_logger("user_api")


@router.get(
    "/profile",
    response_model=Dict[str, Any],
    tags=["用户信息"],
    summary="获取用户资料",
    description="返回当前认证用户的完整个人信息"
)
async def get_user_profile(
    current_user: Dict[str, Any] = Depends(require_authentication),
    request_id: str = Depends(get_current_request_id)
):
    """
    获取用户资料

    返回当前认证用户的完整个人信息，包括基本信息、联系方式、地址等。

    Args:
        current_user: 当前认证用户信息
        request_id: 请求ID，用于日志追踪

    Returns:
        包含用户完整信息的响应：
        - id: 用户ID
        - uuid: 用户UUID
        - phone: 手机号
        - username: 用户名
        - avatar_url: 头像URL
        - province_code: 省份代码
        - city_code: 城市代码
        - address: 详细地址
        - created_at: 创建时间
        - last_login: 最后登录时间

    Raises:
        AuthenticationError: 用户未认证
        ResourceNotFoundError: 用户不存在
    """
    try:
        profile = await user_service.get_user_profile(current_user["user_id"])
        
        struct_logger.info(
            "获取用户资料",
            user_id=current_user["user_id"],
            request_id=request_id
        )
        
        return create_success_response(
            data=profile,
            message="获取用户资料成功",
            request_id=request_id
        )
        
    except Exception as e:
        struct_logger.error(
            "获取用户资料失败",
            user_id=current_user["user_id"],
            error=str(e),
            request_id=request_id
        )
        raise


@router.post("/update-username", response_model=Dict[str, Any], tags=["用户信息"])
async def update_username(
    request_data: UpdateUsernameRequest,
    current_user: Dict[str, Any] = Depends(require_authentication),
    request_id: str = Depends(get_current_request_id)
):
    """
    更新用户名
    设置或修改用户的显示名称
    """
    try:
        result = await user_service.update_username(
            current_user["user_id"], 
            request_data
        )
        
        struct_logger.info(
            "更新用户名",
            user_id=current_user["user_id"],
            new_username=request_data.username,
            request_id=request_id
        )
        
        return create_success_response(
            data=result,
            message=result["message"],
            request_id=request_id
        )
        
    except Exception as e:
        struct_logger.error(
            "更新用户名失败",
            user_id=current_user["user_id"],
            username=request_data.username,
            error=str(e),
            request_id=request_id
        )
        raise


@router.post("/avatar", response_model=Dict[str, Any], tags=["用户信息"])
async def upload_avatar(
    file: UploadFile = File(..., description="头像文件"),
    current_user: Dict[str, Any] = Depends(require_authentication),
    request_id: str = Depends(get_current_request_id)
):
    """
    上传用户头像
    支持 JPG、PNG、GIF、WebP 格式，最大 5MB
    """
    try:
        # 上传头像文件
        upload_result = await file_service.upload_avatar(
            file, 
            current_user["user_id"]
        )
        
        # 更新用户头像URL
        update_result = await user_service.update_avatar(
            current_user["user_id"],
            upload_result["avatar_url"]
        )
        
        struct_logger.info(
            "上传用户头像",
            user_id=current_user["user_id"],
            filename=upload_result["filename"],
            file_size=file.size,
            request_id=request_id
        )
        
        return create_success_response(
            data={
                "avatar_url": update_result["avatar_url"],
                "filename": upload_result["filename"]
            },
            message="头像上传成功",
            request_id=request_id
        )
        
    except Exception as e:
        struct_logger.error(
            "上传头像失败",
            user_id=current_user["user_id"],
            filename=file.filename,
            error=str(e),
            request_id=request_id
        )
        raise


@router.get("/avatar/{file_name}", tags=["用户信息"])
async def get_avatar(
    file_name: str,
    request_id: str = Depends(get_current_request_id)
):
    """
    获取用户头像文件
    直接返回头像文件内容
    """
    try:
        # 验证文件名安全性（防止路径遍历攻击）
        if ".." in file_name or "/" in file_name or "\\" in file_name:
            raise HTTPException(status_code=400, detail="无效的文件名")

        # 获取头像文件
        file_path = await file_service.get_avatar_file(file_name)

        if not file_path or not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="头像文件不存在")

        struct_logger.info(
            "获取头像文件",
            file_name=file_name,
            request_id=request_id
        )

        # 返回文件响应
        return FileResponse(
            path=file_path,
            media_type="image/*",
            headers={
                "Cache-Control": "public, max-age=3600",  # 缓存1小时
                "ETag": f'"{file_name}"'
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        struct_logger.error(
            "获取头像文件失败",
            file_name=file_name,
            error=str(e),
            request_id=request_id
        )
        raise HTTPException(status_code=500, detail="获取头像文件失败")


@router.post("/update-address", response_model=Dict[str, Any], tags=["用户信息"])
async def update_address(
    request_data: UpdateAddressRequest,
    current_user: Dict[str, Any] = Depends(require_authentication),
    request_id: str = Depends(get_current_request_id)
):
    """
    更新用户地址
    设置用户的省市信息
    """
    try:
        result = await user_service.update_address(
            current_user["user_id"],
            request_data
        )
        
        struct_logger.info(
            "更新用户地址",
            user_id=current_user["user_id"],
            province_code=request_data.province_code,
            city_code=request_data.city_code,
            request_id=request_id
        )
        
        return create_success_response(
            data=result,
            message=result["message"],
            request_id=request_id
        )
        
    except Exception as e:
        struct_logger.error(
            "更新地址失败",
            user_id=current_user["user_id"],
            province_code=request_data.province_code,
            city_code=request_data.city_code,
            error=str(e),
            request_id=request_id
        )
        raise


@router.get("/provinces", response_model=Dict[str, Any], tags=["地址信息"])
async def get_provinces(
    request_id: str = Depends(get_current_request_id)
):
    """
    获取省份列表
    返回所有可用的省份信息
    """
    try:
        provinces = await user_service.get_provinces()
        
        return create_success_response(
            data=provinces,
            message="获取省份列表成功",
            request_id=request_id
        )
        
    except Exception as e:
        struct_logger.error(
            "获取省份列表失败",
            error=str(e),
            request_id=request_id
        )
        raise


@router.get("/cities/{province_code}", response_model=Dict[str, Any], tags=["地址信息"])
async def get_cities(
    province_code: str,
    request_id: str = Depends(get_current_request_id)
):
    """
    获取城市列表
    根据省份代码返回该省份下的所有城市
    """
    try:
        cities = await user_service.get_cities(province_code)
        
        return create_success_response(
            data=cities,
            message="获取城市列表成功",
            request_id=request_id
        )
        
    except Exception as e:
        struct_logger.error(
            "获取城市列表失败",
            province_code=province_code,
            error=str(e),
            request_id=request_id
        )
        raise
