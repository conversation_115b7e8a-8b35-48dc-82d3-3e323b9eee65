"""
认证相关的 API 端点
"""

from typing import Dict, Any
from fastapi import APIRouter, Depends, Request
import structlog

from app.schemas.auth import SendCodeRequest, SendCodeResponse, LoginRequest, LoginResponse
from app.schemas.response import create_success_response
from app.services.auth_service import AuthService
from app.services.token_service import TokenService
from app.dependencies import get_current_request_id, require_authentication
from app.utils.logger import BusinessLogger

router = APIRouter()
auth_service = AuthService()
token_service = TokenService()
logger = BusinessLogger("auth_api")
struct_logger = structlog.get_logger("auth_api")


@router.post(
    "/send-code",
    response_model=Dict[str, Any],
    tags=["认证"],
    summary="发送验证码",
    description="向指定手机号发送验证码，用于登录或其他验证场景"
)
async def send_verification_code(
    request_data: SendCodeRequest,
    request: Request,
    request_id: str = Depends(get_current_request_id)
):
    """
    发送验证码

    向指定手机号发送验证码，支持以下用途：
    - login: 登录验证
    - register: 注册验证
    - reset_password: 重置密码验证

    Args:
        request_data: 发送验证码请求，包含手机号和用途
        request: FastAPI请求对象
        request_id: 请求ID，用于日志追踪

    Returns:
        包含发送结果的响应

    Raises:
        ValidationError: 手机号格式错误或用途无效
        BusinessLogicError: 发送频率过高或其他业务错误
    """
    try:
        # 获取客户端IP
        client_ip = request.client.host if request.client else "unknown"
        
        # 发送验证码
        result = await auth_service.send_verification_code(request_data, client_ip)
        
        struct_logger.info(
            "验证码发送请求",
            phone=request_data.phone,
            purpose=request_data.purpose,
            client_ip=client_ip,
            request_id=request_id
        )
        
        return create_success_response(
            data=result,
            message=result["message"],
            request_id=request_id
        )
        
    except Exception as e:
        struct_logger.error(
            "发送验证码失败",
            phone=request_data.phone,
            error=str(e),
            request_id=request_id
        )
        raise


@router.post(
    "/login",
    response_model=Dict[str, Any],
    tags=["认证"],
    summary="验证码登录",
    description="使用手机号和验证码进行登录认证"
)
async def login_with_verification_code(
    request_data: LoginRequest,
    request: Request,
    request_id: str = Depends(get_current_request_id)
):
    """
    验证码登录

    使用手机号和验证码进行登录认证，成功后返回访问令牌和刷新令牌。

    Args:
        request_data: 登录请求，包含手机号和验证码
        request: FastAPI请求对象
        request_id: 请求ID，用于日志追踪

    Returns:
        包含以下信息的响应：
        - access_token: 访问令牌（24小时有效）
        - refresh_token: 刷新令牌（7天有效）
        - token_type: 令牌类型（bearer）
        - expires_in: 访问令牌过期时间（秒）
        - user_info: 用户基本信息

    Raises:
        ValidationError: 手机号或验证码格式错误
        AuthenticationError: 验证码错误或已过期
        BusinessLogicError: 用户不存在或账户被禁用
    """
    try:
        # 获取客户端IP
        client_ip = request.client.host if request.client else "unknown"
        
        # 执行登录
        result = await auth_service.login_with_code(request_data, client_ip)
        
        struct_logger.info(
            "用户登录请求",
            phone=request_data.phone,
            client_ip=client_ip,
            success=result["success"],
            request_id=request_id
        )
        
        return create_success_response(
            data=result,
            message=result["message"],
            request_id=request_id
        )
        
    except Exception as e:
        struct_logger.error(
            "用户登录失败",
            phone=request_data.phone,
            error=str(e),
            request_id=request_id
        )
        raise


@router.post(
    "/refresh",
    response_model=Dict[str, Any],
    tags=["认证"],
    summary="刷新访问令牌",
    description="使用刷新令牌获取新的访问令牌"
)
async def refresh_access_token(
    refresh_token: str,
    request_id: str = Depends(get_current_request_id)
):
    """
    刷新访问令牌

    使用有效的刷新令牌获取新的访问令牌，延长用户会话时间。

    Args:
        refresh_token: 刷新令牌（7天有效期）
        request_id: 请求ID，用于日志追踪

    Returns:
        包含以下信息的响应：
        - access_token: 新的访问令牌（24小时有效）
        - token_type: 令牌类型（bearer）
        - expires_in: 访问令牌过期时间（秒）
        - refreshed_at: 刷新时间

    Raises:
        ValidationError: 刷新令牌格式错误或无效
        AuthenticationError: 刷新令牌已过期或被撤销
        BusinessLogicError: 用户不存在或账户被禁用
    """
    try:
        # 刷新访问令牌
        result = await token_service.refresh_access_token(refresh_token)

        struct_logger.info(
            "访问令牌刷新成功",
            request_id=request_id
        )

        return create_success_response(
            data=result,
            message="令牌刷新成功",
            request_id=request_id
        )

    except Exception as e:
        struct_logger.error(
            "刷新令牌失败",
            error=str(e),
            request_id=request_id
        )
        raise


@router.post(
    "/logout",
    response_model=Dict[str, Any],
    tags=["认证"],
    summary="用户登出",
    description="注销当前用户会话，撤销访问令牌"
)
async def logout(
    request: Request,
    current_user: Dict[str, Any] = Depends(require_authentication),
    request_id: str = Depends(get_current_request_id)
):
    """
    用户登出

    注销当前用户会话，撤销访问令牌和刷新令牌，确保安全退出。

    Args:
        request: FastAPI请求对象，用于获取Authorization头
        current_user: 当前认证用户信息
        request_id: 请求ID，用于日志追踪

    Returns:
        包含登出结果的响应：
        - logout_time: 登出时间
        - message: 登出状态信息

    Raises:
        AuthenticationError: 用户未认证或令牌无效
        BusinessLogicError: 令牌撤销失败
    """
    try:
        # 从请求头获取访问令牌
        authorization = request.headers.get("Authorization")
        if authorization and authorization.startswith("Bearer "):
            access_token = authorization[7:]  # 移除 "Bearer " 前缀

            # 撤销令牌
            success = await token_service.revoke_token(access_token)

            if success:
                struct_logger.info(
                    "用户登出成功",
                    user_id=current_user.get("user_id"),
                    request_id=request_id
                )

                return create_success_response(
                    data={"logout_time": __import__('app.utils.timezone', fromlist=['get_china_now']).get_china_now().isoformat()},
                    message="登出成功",
                    request_id=request_id
                )
            else:
                return create_success_response(
                    data={"message": "令牌已失效"},
                    message="登出成功",
                    request_id=request_id
                )
        else:
            return create_success_response(
                data={"message": "无有效令牌"},
                message="登出成功",
                request_id=request_id
            )
        
    except Exception as e:
        struct_logger.error(
            "用户登出失败",
            error=str(e),
            request_id=request_id
        )
        raise
