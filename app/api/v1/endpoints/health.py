"""
健康检查端点
提供应用健康状态和监控信息
"""

from fastapi import APIRouter, Depends
from typing import Dict, Any
import time
import psutil
import structlog

from app.schemas.response import HealthCheckResponse, MetricsResponse, create_success_response
from app.config import settings
from app.dependencies import get_current_request_id
from app.utils.logger import BusinessLogger

router = APIRouter()
logger = BusinessLogger("health")
struct_logger = structlog.get_logger("health")


@router.get(
    "/",
    response_model=HealthCheckResponse,
    tags=["健康检查"],
    summary="基础健康检查",
    description="返回应用的基本状态信息，包括数据库连接、缓存状态等"
)
async def health_check(request_id: str = Depends(get_current_request_id)):
    """
    基础健康检查

    返回应用的基本状态信息，用于监控系统判断服务是否正常运行。

    Args:
        request_id: 请求ID，用于日志追踪

    Returns:
        包含以下信息的健康检查响应：
        - status: 服务状态（healthy/unhealthy）
        - app_name: 应用名称
        - version: 应用版本
        - environment: 运行环境
        - timestamp: 检查时间戳
        - uptime: 运行时间
        - database: 数据库连接状态
        - cache: 缓存连接状态
    """
    try:
        # 检查数据库连接状态
        database_status = await _check_database_connection()
        
        # 检查缓存状态
        cache_status = await _check_cache_connection()
        
        # 计算运行时间
        uptime = _get_uptime()
        
        health_data = {
            "status": "healthy" if database_status == "connected" else "unhealthy",
            "app_name": settings.app_name,
            "version": settings.app_version,
            "environment": settings.environment,
            "timestamp": time.time(),
            "uptime": uptime,
            "database": database_status,
            "cache": cache_status
        }
        
        # 记录健康检查
        logger.log_operation(
            operation="health_check",
            details={"status": health_data["status"]},
            success=True
        )
        
        return health_data
        
    except Exception as e:
        struct_logger.error("健康检查失败", error=str(e), request_id=request_id)
        
        return {
            "status": "unhealthy",
            "app_name": settings.app_name,
            "version": settings.app_version,
            "environment": settings.environment,
            "timestamp": time.time(),
            "uptime": "unknown",
            "database": "error",
            "cache": "error",
            "error": str(e)
        }


@router.get(
    "/detailed",
    tags=["健康检查"],
    summary="详细健康检查",
    description="返回更详细的系统状态信息，包括系统资源、应用指标等"
)
async def detailed_health_check(request_id: str = Depends(get_current_request_id)):
    """
    详细健康检查

    返回更详细的系统状态信息，包括系统资源使用情况、应用性能指标等。

    Args:
        request_id: 请求ID，用于日志追踪

    Returns:
        包含详细系统信息的响应：
        - 基础健康信息
        - 系统资源信息（CPU、内存、磁盘）
        - 应用性能指标
        - 各组件详细检查结果
    """
    try:
        # 基础健康信息
        basic_health = await health_check(request_id)
        
        # 系统资源信息
        system_info = _get_system_info()
        
        # 应用指标
        app_metrics = _get_app_metrics()
        
        detailed_data = {
            **basic_health,
            "system": system_info,
            "metrics": app_metrics,
            "checks": {
                "database": await _detailed_database_check(),
                "cache": await _detailed_cache_check(),
                "disk_space": _check_disk_space(),
                "memory": _check_memory_usage()
            }
        }
        
        logger.log_operation(
            operation="detailed_health_check",
            details={"status": detailed_data["status"]},
            success=True
        )
        
        return create_success_response(
            data=detailed_data,
            message="详细健康检查完成",
            request_id=request_id
        )
        
    except Exception as e:
        struct_logger.error("详细健康检查失败", error=str(e), request_id=request_id)
        raise


@router.get("/metrics", response_model=MetricsResponse, tags=["监控"])
async def get_metrics(request_id: str = Depends(get_current_request_id)):
    """
    获取应用指标
    返回性能和使用统计信息
    """
    try:
        metrics_data = {
            "requests_total": _get_total_requests(),
            "requests_duration": _get_average_request_duration(),
            "active_connections": _get_active_connections(),
            "memory_usage": _get_memory_usage_info()
        }
        
        logger.log_operation(
            operation="get_metrics",
            success=True
        )
        
        return metrics_data
        
    except Exception as e:
        struct_logger.error("获取指标失败", error=str(e), request_id=request_id)
        raise


@router.get("/readiness", tags=["健康检查"])
async def readiness_check(request_id: str = Depends(get_current_request_id)):
    """
    就绪检查
    检查应用是否准备好接收流量
    """
    try:
        checks = {
            "database": await _check_database_connection() == "connected",
            "cache": await _check_cache_connection() == "connected",
            "configuration": _check_configuration(),
            "dependencies": await _check_external_dependencies()
        }
        
        is_ready = all(checks.values())
        
        result = {
            "ready": is_ready,
            "checks": checks,
            "timestamp": time.time()
        }
        
        logger.log_operation(
            operation="readiness_check",
            details={"ready": is_ready},
            success=True
        )
        
        return create_success_response(
            data=result,
            message="就绪检查完成",
            request_id=request_id
        )
        
    except Exception as e:
        struct_logger.error("就绪检查失败", error=str(e), request_id=request_id)
        raise


@router.get("/liveness", tags=["健康检查"])
async def liveness_check(request_id: str = Depends(get_current_request_id)):
    """
    存活检查
    检查应用是否仍在运行
    """
    try:
        # 简单的存活检查
        result = {
            "alive": True,
            "timestamp": time.time(),
            "pid": _get_process_id(),
            "uptime": _get_uptime()
        }
        
        return create_success_response(
            data=result,
            message="存活检查通过",
            request_id=request_id
        )
        
    except Exception as e:
        struct_logger.error("存活检查失败", error=str(e), request_id=request_id)
        raise


# 私有辅助函数
async def _check_database_connection() -> str:
    """检查数据库连接"""
    try:
        # 这里应该实际检查数据库连接
        # 例如：执行一个简单的查询
        # await database.execute("SELECT 1")
        return "connected"
    except Exception:
        return "disconnected"


async def _check_cache_connection() -> str:
    """检查缓存连接"""
    try:
        # 这里应该实际检查缓存连接
        # 例如：Redis ping
        return "connected"
    except Exception:
        return "disconnected"


def _get_uptime() -> str:
    """获取应用运行时间"""
    try:
        # 这里应该计算实际的运行时间
        # 可以在应用启动时记录开始时间
        return "运行中"
    except Exception:
        return "unknown"


def _get_system_info() -> Dict[str, Any]:
    """获取系统信息"""
    try:
        return {
            "cpu_count": psutil.cpu_count(),
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory_total": psutil.virtual_memory().total,
            "memory_available": psutil.virtual_memory().available,
            "memory_percent": psutil.virtual_memory().percent,
            "disk_usage": psutil.disk_usage('/').percent,
            "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
        }
    except Exception:
        return {"error": "无法获取系统信息"}


def _get_app_metrics() -> Dict[str, Any]:
    """获取应用指标"""
    return {
        "total_requests": "待实现",
        "error_rate": "待实现",
        "response_time_avg": "待实现",
        "active_sessions": "待实现"
    }


async def _detailed_database_check() -> Dict[str, Any]:
    """详细数据库检查"""
    try:
        # 这里应该执行更详细的数据库检查
        return {
            "status": "healthy",
            "response_time_ms": 5,
            "connection_pool": "available"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }


async def _detailed_cache_check() -> Dict[str, Any]:
    """详细缓存检查"""
    try:
        # 这里应该执行更详细的缓存检查
        return {
            "status": "healthy",
            "response_time_ms": 2,
            "memory_usage": "normal"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }


def _check_disk_space() -> Dict[str, Any]:
    """检查磁盘空间"""
    try:
        disk_usage = psutil.disk_usage('/')
        usage_percent = (disk_usage.used / disk_usage.total) * 100
        
        return {
            "status": "healthy" if usage_percent < 90 else "warning",
            "usage_percent": round(usage_percent, 2),
            "free_gb": round(disk_usage.free / (1024**3), 2),
            "total_gb": round(disk_usage.total / (1024**3), 2)
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }


def _check_memory_usage() -> Dict[str, Any]:
    """检查内存使用"""
    try:
        memory = psutil.virtual_memory()
        
        return {
            "status": "healthy" if memory.percent < 90 else "warning",
            "usage_percent": memory.percent,
            "available_gb": round(memory.available / (1024**3), 2),
            "total_gb": round(memory.total / (1024**3), 2)
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }


def _check_configuration() -> bool:
    """检查配置"""
    try:
        # 检查关键配置项
        return bool(settings.secret_key and settings.database_url)
    except Exception:
        return False


async def _check_external_dependencies() -> bool:
    """检查外部依赖"""
    try:
        # 这里应该检查外部服务的可用性
        return True
    except Exception:
        return False


def _get_total_requests() -> str:
    """获取总请求数"""
    # 这里应该从指标收集器获取实际数据
    return "待实现"


def _get_average_request_duration() -> str:
    """获取平均请求时间"""
    # 这里应该从指标收集器获取实际数据
    return "待实现"


def _get_active_connections() -> str:
    """获取活跃连接数"""
    # 这里应该从连接池获取实际数据
    return "待实现"


def _get_memory_usage_info() -> str:
    """获取内存使用信息"""
    try:
        memory = psutil.virtual_memory()
        return f"{memory.percent}% ({round(memory.used / (1024**3), 2)}GB / {round(memory.total / (1024**3), 2)}GB)"
    except Exception:
        return "unknown"


def _get_process_id() -> int:
    """获取进程ID"""
    import os
    return os.getpid()
