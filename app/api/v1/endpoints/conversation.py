"""
AI对话会话相关的 API 端点
"""

from typing import Dict, Any, List
from fastapi import APIRouter, Depends, Request
import structlog

from app.schemas.conversation import (
    CreateConversationRequest,
    DeleteConversationRequest,
    CreateConversationResponse,
    GetConversationsResponse
)
from app.schemas.response import create_success_response
from app.services.conversation_service import ConversationService
from app.dependencies import get_current_request_id, require_authentication
from app.utils.logger import BusinessLogger

router = APIRouter()
conversation_service = ConversationService()
logger = BusinessLogger("conversation_api")
struct_logger = structlog.get_logger("conversation_api")


@router.post(
    "/",
    response_model=Dict[str, Any],
    tags=["AI对话会话"],
    summary="创建AI对话会话",
    description="用户通过认证后创建一个新的AI对话会话"
)
async def create_conversation(
    request_data: CreateConversationRequest,
    request: Request,
    current_user: Dict[str, Any] = Depends(require_authentication),
    request_id: str = Depends(get_current_request_id)
):
    """
    创建AI对话会话

    用户通过请求头认证后，创建一个新的AI对话会话条目。

    Args:
        request_data: 创建会话请求，包含会话标题
        request: FastAPI请求对象
        current_user: 当前认证用户信息
        request_id: 请求ID，用于日志追踪

    Returns:
        包含以下信息的响应：
        - conversation_id: 会话UUID
        - title: 会话标题
        - created_at: 创建时间
        - user_id: 用户ID

    Raises:
        ValidationError: 请求数据验证失败
        AuthenticationError: 用户未认证
        BusinessLogicError: 业务逻辑错误
    """
    try:
        # 获取用户ID
        user_id = current_user.get("user_id")
        
        # 创建会话
        conversation_data = conversation_service.create_conversation(
            request_data,
            user_id
        )
        
        struct_logger.info(
            "AI会话创建请求",
            user_id=user_id,
            title=request_data.title,
            conversation_id=conversation_data["conversation_id"],
            request_id=request_id
        )
        
        return create_success_response(
            data=conversation_data,
            message="AI对话会话创建成功",
            request_id=request_id
        )
        
    except Exception as e:
        struct_logger.error(
            "创建AI会话失败",
            user_id=current_user.get("user_id"),
            title=request_data.title,
            error=str(e),
            request_id=request_id
        )
        raise


@router.get(
    "/",
    response_model=Dict[str, Any],
    tags=["AI对话会话"],
    summary="获取用户会话列表",
    description="根据用户认证信息获取所有未删除的AI对话会话列表"
)
async def get_conversations(
    request: Request,
    current_user: Dict[str, Any] = Depends(require_authentication),
    request_id: str = Depends(get_current_request_id)
):
    """
    获取用户会话列表

    根据用户的认证信息，查询所有 is_deleted=False 的AI对话会话，
    并返回会话列表。

    Args:
        request: FastAPI请求对象
        current_user: 当前认证用户信息
        request_id: 请求ID，用于日志追踪

    Returns:
        包含以下信息的响应：
        - data: 会话列表，每个会话包含：
          - id: 会话ID
          - conversation_id: 会话UUID
          - title: 会话标题
          - created_at: 创建时间
          - updated_at: 更新时间

    Raises:
        AuthenticationError: 用户未认证
        BusinessLogicError: 业务逻辑错误
    """
    try:
        # 获取用户ID
        user_id = current_user.get("user_id")
        
        # 获取用户会话列表
        conversations = conversation_service.get_user_conversations(user_id)
        
        struct_logger.info(
            "获取用户会话列表请求",
            user_id=user_id,
            conversation_count=len(conversations),
            request_id=request_id
        )
        
        return create_success_response(
            data=conversations,
            message=f"查询成功，共找到 {len(conversations)} 个会话",
            request_id=request_id
        )
        
    except Exception as e:
        struct_logger.error(
            "获取用户会话列表失败",
            user_id=current_user.get("user_id"),
            error=str(e),
            request_id=request_id
        )
        raise


@router.get(
    "/{conversation_id}",
    response_model=Dict[str, Any],
    tags=["AI对话会话"],
    summary="获取会话详情",
    description="根据会话ID获取特定会话的详细信息"
)
async def get_conversation_detail(
    conversation_id: str,
    request: Request,
    current_user: Dict[str, Any] = Depends(require_authentication),
    request_id: str = Depends(get_current_request_id)
):
    """
    获取会话详情

    根据会话ID获取特定会话的详细信息，仅返回属于当前用户的会话。

    Args:
        conversation_id: 会话UUID
        request: FastAPI请求对象
        current_user: 当前认证用户信息
        request_id: 请求ID，用于日志追踪

    Returns:
        包含会话详细信息的响应

    Raises:
        AuthenticationError: 用户未认证
        NotFoundError: 会话不存在或不属于当前用户
        BusinessLogicError: 业务逻辑错误
    """
    try:
        # 获取用户ID
        user_id = current_user.get("user_id")
        
        # 获取会话详情
        conversation = conversation_service.get_conversation_by_id(
            conversation_id,
            user_id
        )
        
        if not conversation:
            from app.core.exceptions import NotFoundError
            raise NotFoundError("会话不存在或不属于当前用户")
        
        struct_logger.info(
            "获取会话详情请求",
            user_id=user_id,
            conversation_id=conversation_id,
            request_id=request_id
        )
        
        return create_success_response(
            data=conversation,
            message="获取会话详情成功",
            request_id=request_id
        )
        
    except Exception as e:
        struct_logger.error(
            "获取会话详情失败",
            user_id=current_user.get("user_id"),
            conversation_id=conversation_id,
            error=str(e),
            request_id=request_id
        )
        raise


@router.post(
    "/delete",
    response_model=Dict[str, Any],
    tags=["AI对话会话"],
    summary="删除AI对话会话",
    description="删除指定的AI对话会话（软删除）"
)
async def delete_conversation(
    request_data: DeleteConversationRequest,
    request: Request,
    current_user: Dict[str, Any] = Depends(require_authentication),
    request_id: str = Depends(get_current_request_id)
):
    """
    删除AI对话会话

    用户通过请求头认证后，删除指定的AI对话会话。
    使用软删除方式，将is_deleted字段设为True。

    Args:
        request_data: 删除会话请求，包含conversation_id
        request: FastAPI请求对象
        current_user: 当前认证用户信息
        request_id: 请求ID，用于日志追踪

    Returns:
        包含以下信息的响应：
        - conversation_id: 被删除的会话UUID
        - title: 会话标题
        - deleted_at: 删除时间
        - message: 操作结果消息

    Raises:
        ValidationError: 请求数据验证失败
        AuthenticationError: 用户未认证
        BusinessLogicError: 业务逻辑错误（会话不存在或无权限）
    """
    try:
        # 获取用户ID
        user_id = current_user.get("user_id")

        # 删除会话
        delete_result = conversation_service.delete_conversation(
            request_data,
            user_id
        )

        struct_logger.info(
            "AI会话删除请求",
            user_id=user_id,
            conversation_id=request_data.conversation_id,
            request_id=request_id
        )

        return create_success_response(
            data=delete_result,
            message="AI对话会话删除成功",
            request_id=request_id
        )

    except Exception as e:
        struct_logger.error(
            "删除AI会话失败",
            user_id=current_user.get("user_id"),
            conversation_id=request_data.conversation_id,
            error=str(e),
            request_id=request_id
        )
        raise
