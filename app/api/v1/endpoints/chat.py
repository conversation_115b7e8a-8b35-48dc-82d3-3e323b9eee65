"""
聊天相关的 API 端点
"""

import json
import re
import uuid
import asyncio
import traceback
from typing import Dict, Any, List
from fastapi import APIRouter, Depends, Request, HTTPException, Query
from fastapi.responses import StreamingResponse
import structlog

from app.schemas.chat import (
    ChatCompletionRequest,
    ConversationHistoryResponse,
    SummaryTopicsRequest,
    SummaryTopicsResponse
)
from app.schemas.response import create_success_response, SuccessResponse
from app.services.chat_service import ChatService
from app.dependencies import get_current_request_id, require_authentication
from app.utils.logger import BusinessLogger

router = APIRouter()
chat_service = ChatService()
logger = BusinessLogger("chat_api")
struct_logger = structlog.get_logger("chat_api")


async def process_recommendation_data(car_data_list, task_data, conversation_id, message_id, chat_message_uuid):
    """处理收集到的推荐数据"""
    struct_logger.info(
        "开始处理推荐数据",
        car_data_list_type=type(car_data_list).__name__,
        car_data_count=len(car_data_list) if car_data_list else 0,
        task_data=task_data,
        conversation_id=conversation_id,
        message_id=message_id,
        chat_message_uuid=chat_message_uuid
    )
    
    if not car_data_list and not task_data:
        struct_logger.warning(
            "没有推荐数据需要处理",
            conversation_id=conversation_id,
            message_id=message_id
        )
        return

    try:
        # 如果有任务数据但没有车辆数据，也需要保存任务数据
        if task_data and not car_data_list:
            struct_logger.info(
                "只有任务数据，没有车辆数据",
                task_type=task_data.get("active_task_type"),
                slots=task_data.get("slots", {}),
                conversation_id=conversation_id,
                message_id=message_id
            )
            # 保存任务数据（作为推荐记录）
            chat_service.save_car_recommendation(
                conversation_id,
                message_id,
                chat_message_uuid,
                task_data  # 直接传递任务数据
            )
            return
        
        if task_data and task_data.get("active_task_type") == "compare_car":
            # 车辆对比：所有车辆作为一个对比组
            struct_logger.info(
                "处理车辆对比数据",
                car_count=len(car_data_list),
                car_ids=[car.get("id") for car in car_data_list if isinstance(car, dict)],
                conversation_id=conversation_id,
                message_id=message_id
            )
            chat_service.save_car_comparison(
                conversation_id,
                message_id,
                chat_message_uuid,
                car_data_list,
                task_data.get("slots", {})
            )
        else:
            # 单车推荐或多车推荐
            if len(car_data_list) == 1:
                # 单车推荐：直接传递单个车辆数据
                struct_logger.info(
                    "处理单车推荐数据",
                    car_id=car_data_list[0].get("id") if isinstance(car_data_list[0], dict) else None,
                    car_brand=car_data_list[0].get("brand") if isinstance(car_data_list[0], dict) else None,
                    conversation_id=conversation_id,
                    message_id=message_id
                )
                chat_service.save_car_recommendation(
                    conversation_id,
                    message_id,
                    chat_message_uuid,
                    car_data_list[0]  # 传递单个车辆数据
                )
            else:
                # 多车推荐：传递整个数组
                struct_logger.info(
                    "处理多车推荐数据",
                    car_count=len(car_data_list),
                    car_ids=[car.get("id") for car in car_data_list if isinstance(car, dict)],
                    conversation_id=conversation_id,
                    message_id=message_id
                )
                chat_service.save_car_recommendation(
                    conversation_id,
                    message_id,
                    chat_message_uuid,
                    car_data_list  # 传递整个车辆数组
                )
                
        # 如果同时有任务数据，也保存任务数据
        if task_data:
            struct_logger.info(
                "同时保存任务数据",
                task_type=task_data.get("active_task_type"),
                slots=task_data.get("slots", {}),
                conversation_id=conversation_id,
                message_id=message_id
            )
            # 保存任务数据（作为单独的推荐记录）
            chat_service.save_car_recommendation(
                conversation_id,
                message_id,
                chat_message_uuid,
                task_data  # 传递任务数据
            )
            
    except Exception as e:
        struct_logger.error(
            "处理推荐数据失败",
            error=str(e),
            error_type=type(e).__name__,
            car_count=len(car_data_list) if car_data_list else 0,
            task_type=task_data.get("active_task_type") if task_data else None,
            conversation_id=conversation_id,
            message_id=message_id,
            traceback=traceback.format_exc()
        )


@router.post(
    "/completions",
    tags=["聊天"],
    summary="聊天完成接口",
    description="流式聊天完成接口，实时返回AI回复内容"
)
async def chat_completions(
    request_data: ChatCompletionRequest,
    request: Request,
    current_user: Dict[str, Any] = Depends(require_authentication),
    request_id: str = Depends(get_current_request_id)
):
    """
    聊天完成接口

    接收用户消息，调用AI服务获取流式回复，同时存储对话数据。

    Args:
        request_data: 聊天请求，包含消息内容和对话ID
        request: FastAPI请求对象
        current_user: 当前认证用户信息
        request_id: 请求ID，用于日志追踪

    Returns:
        流式响应，格式为SSE (Server-Sent Events)

    Raises:
        ValidationError: 请求数据验证失败
        AuthenticationError: 用户未认证
        BusinessLogicError: 业务逻辑错误
    """
    try:
        # 从认证信息中获取用户信息
        user_id = current_user.get("user_id")  # 整数ID，用于内部数据库操作
        user_uuid = current_user.get("user_uuid")  # UUID字符串，用于外部AI服务

        struct_logger.info(
            "聊天完成请求",
            user_id=user_id,
            user_uuid=user_uuid,
            conversation_id=request_data.conversation_id,
            message_length=len(request_data.message),
            request_id=request_id
        )
        
        # 保存用户消息
        chat_service.save_user_message(request_data, user_id)

        # 生成消息ID
        message_id = str(uuid.uuid4())

        # 创建AI助手消息记录
        assistant_message = chat_service.create_assistant_message(
            request_data.conversation_id,
            user_id,
            message_id
        )
        
        async def collect_ai_response():
            """收集AI服务的完整响应数据"""
            accumulated_content = ""
            collected_car_data = []
            task_data = None
            original_chunks = []  # 保存原始chunks用于重放
            stop_received = False  # 跟踪是否已收到结束标记

            try:
                # 调用AI服务获取流式响应
                async for chunk in chat_service.call_ai_service(
                    request_data.message,
                    request_data.conversation_id,
                    user_uuid,
                    message_id
                ):
                    # 保存原始chunk用于后续重放
                    original_chunks.append(chunk)

                    # 处理可能包含多行的chunk
                    lines = chunk.strip().split('\n')
                    for line in lines:
                        line = line.strip()
                        if not line:
                            continue

                        # 解析响应数据
                        if line.startswith("data: "):
                            data_content = line[6:].strip()

                            if data_content == "[DONE]":
                                break

                            try:
                                # 解析JSON数据
                                response_data = json.loads(data_content)

                                if "data" in response_data and "choices" in response_data["data"]:
                                    choices = response_data["data"]["choices"]
                                    if choices and len(choices) > 0:
                                        choice = choices[0]
                                        delta = choice.get("delta", "")
                                        finish_reason = choice.get("finish_reason")

                                    # 先处理delta内容 - 根据当前的stop_received状态决定处理方式
                                    if delta and isinstance(delta, str):
                                        # 如果还没收到结束标记，直接拼合所有内容
                                        if not stop_received:
                                            accumulated_content += delta
                                            struct_logger.debug(
                                                "结束标记前的内容直接拼合",
                                                delta=delta,
                                                delta_length=len(delta),
                                                accumulated_length_after=len(accumulated_content),
                                                conversation_id=request_data.conversation_id,
                                                message_id=message_id
                                            )
                                        else:
                                            # 收到结束标记后，进行复杂的特殊数据处理
                                            struct_logger.debug(
                                                "结束标记后处理特殊数据",
                                                delta=delta,
                                                delta_length=len(delta),
                                                accumulated_length_before=len(accumulated_content),
                                                conversation_id=request_data.conversation_id,
                                                message_id=message_id
                                            )

                                            # 只有当delta看起来像是完整的JSON结构时才尝试解析
                                            # 检查是否包含JSON的特征（以{或[开头，以}或]结尾）
                                            delta_stripped = delta.strip()
                                            is_json_structure = ((delta_stripped.startswith('{') and delta_stripped.endswith('}')) or
                                                               (delta_stripped.startswith('[') and delta_stripped.endswith(']')))

                                            if is_json_structure:
                                                try:
                                                    # 尝试解析delta为JSON，检查是否是特殊数据
                                                    parsed_delta = json.loads(delta)
                                                    is_special_data = False

                                                    # 检查是否是车辆数据数组
                                                    if isinstance(parsed_delta, list) and len(parsed_delta) > 0:
                                                        first_item = parsed_delta[0]
                                                        if isinstance(first_item, dict) and "id" in first_item:
                                                            # 车辆数据数组 - 收集但不添加到文本内容
                                                            collected_car_data.extend(parsed_delta)
                                                            is_special_data = True
                                                            struct_logger.info(
                                                                "识别到车辆数据",
                                                                car_count=len(parsed_delta),
                                                                total_collected=len(collected_car_data),
                                                                conversation_id=request_data.conversation_id,
                                                                message_id=message_id
                                                            )

                                                    # 检查是否是任务类型数据
                                                    elif isinstance(parsed_delta, dict) and "active_task_type" in parsed_delta:
                                                        # 任务类型数据 - 收集但不添加到文本内容
                                                        task_data = parsed_delta
                                                        is_special_data = True
                                                        struct_logger.info(
                                                            "识别到任务类型数据",
                                                            task_type=parsed_delta.get("active_task_type"),
                                                            slots=parsed_delta.get("slots", {}),
                                                            conversation_id=request_data.conversation_id,
                                                            message_id=message_id
                                                        )

                                                    # 只有非特殊数据才添加到文本内容
                                                    if not is_special_data:
                                                        accumulated_content += delta
                                                        struct_logger.debug(
                                                            "JSON数据作为文本添加",
                                                            delta=delta,
                                                            accumulated_length_after=len(accumulated_content),
                                                            conversation_id=request_data.conversation_id,
                                                            message_id=message_id
                                                        )
                                                    else:
                                                        struct_logger.debug(
                                                            "跳过特殊JSON数据",
                                                            delta_preview=delta[:100] if len(delta) > 100 else delta,
                                                            is_car_data=len(collected_car_data) > 0,
                                                            is_task_data=task_data is not None,
                                                            conversation_id=request_data.conversation_id,
                                                            message_id=message_id
                                                        )
                                                except json.JSONDecodeError:
                                                    # 解析失败，作为普通文本处理
                                                    accumulated_content += delta
                                                    struct_logger.debug(
                                                        "JSON解析失败，作为文本添加",
                                                        delta=delta,
                                                        accumulated_length_after=len(accumulated_content),
                                                        conversation_id=request_data.conversation_id,
                                                        message_id=message_id
                                                    )
                                            else:
                                                # 不是JSON结构，直接作为普通文本
                                                accumulated_content += delta
                                                struct_logger.debug(
                                                    "普通文本添加",
                                                    delta=delta,
                                                    accumulated_length_after=len(accumulated_content),
                                                    conversation_id=request_data.conversation_id,
                                                    message_id=message_id
                                                )

                                    # 处理完delta内容后，检查是否是结束标记
                                    if finish_reason == "stop":
                                        stop_received = True
                                        struct_logger.info(
                                            "收到结束标记，后续消息将进行特殊数据处理",
                                            finish_reason=finish_reason,
                                            conversation_id=request_data.conversation_id,
                                            message_id=message_id
                                        )

                            except json.JSONDecodeError as e:
                                struct_logger.warning(
                                    "解析响应数据失败，尝试提取delta内容",
                                    data_content=data_content,
                                    error=str(e),
                                    conversation_id=request_data.conversation_id,
                                    message_id=message_id
                                )

                                # 尝试从格式错误的JSON中提取delta内容
                                if not stop_received:  # 只有在结束标记前才提取
                                    # 使用正则表达式提取delta字段的值
                                    delta_match = re.search(r'"delta":\s*"([^"]*)"', data_content)
                                    if delta_match:
                                        extracted_delta = delta_match.group(1)
                                        accumulated_content += extracted_delta
                                        struct_logger.info(
                                            "从格式错误的JSON中成功提取delta内容",
                                            extracted_delta=extracted_delta,
                                            data_content_preview=data_content[:200] if len(data_content) > 200 else data_content,
                                            conversation_id=request_data.conversation_id,
                                            message_id=message_id
                                        )
                                    else:
                                        struct_logger.warning(
                                            "无法从格式错误的JSON中提取delta内容",
                                            data_content_preview=data_content[:200] if len(data_content) > 200 else data_content,
                                            conversation_id=request_data.conversation_id,
                                            message_id=message_id
                                        )

                return {
                    "content": accumulated_content,
                    "car_data": collected_car_data,
                    "task_data": task_data,
                    "original_chunks": original_chunks
                }

            except Exception as e:
                struct_logger.error(
                    "收集AI响应失败",
                    error=str(e),
                    conversation_id=request_data.conversation_id,
                    message_id=message_id
                )
                raise

        async def replay_response_to_client(original_chunks, processed_car_data, processed_task_data):
            """重放响应给客户端，使用处理后的推荐数据"""
            # 记录已经发送的特殊数据，避免重复
            sent_car_data = False
            sent_task_data = False
            
            for chunk in original_chunks:
                # 处理每行数据
                lines = chunk.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    if not line:
                        continue
                        
                    if line.startswith("data: "):
                        data_content = line[6:].strip()

                        if data_content == "[DONE]":
                            # 在发送[DONE]之前，先发送处理后的推荐数据
                            if not sent_car_data and processed_car_data:
                                for car_data in processed_car_data:
                                    recommendation_response = {
                                        "data": {
                                            "user_id": user_uuid,
                                            "message_id": message_id,
                                            "conversation_id": request_data.conversation_id,
                                            "choices": [{"delta": json.dumps(car_data, ensure_ascii=False)}],
                                            "usage": {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
                                        },
                                        "status": {"code": 0, "message": "OK"}
                                    }
                                    yield f"data: {json.dumps(recommendation_response, ensure_ascii=False)}\n\n"
                                sent_car_data = True

                            # 发送处理后的任务数据
                            if not sent_task_data and processed_task_data:
                                task_response = {
                                    "data": {
                                        "user_id": user_uuid,
                                        "message_id": message_id,
                                        "conversation_id": request_data.conversation_id,
                                        "choices": [{"delta": json.dumps(processed_task_data, ensure_ascii=False)}],
                                        "usage": {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
                                    },
                                    "status": {"code": 0, "message": "OK"}
                                }
                                yield f"data: {json.dumps(task_response, ensure_ascii=False)}\n\n"
                                sent_task_data = True
                                
                            yield f"{line}\n"
                            break

                        try:
                            # 解析JSON数据
                            response_data = json.loads(data_content)

                            if "data" in response_data and "choices" in response_data["data"]:
                                choices = response_data["data"]["choices"]
                                if choices and len(choices) > 0:
                                    delta = choices[0].get("delta", "")

                                    # 检查是否是特殊数据（车辆数据或任务数据）
                                    is_special_data = False
                                    if delta and isinstance(delta, str):
                                        delta_stripped = delta.strip()
                                        # 只有当delta看起来像是完整的JSON结构时才尝试解析
                                        is_json_structure = ((delta_stripped.startswith('{') and delta_stripped.endswith('}')) or
                                                           (delta_stripped.startswith('[') and delta_stripped.endswith(']')))
                                        
                                        if is_json_structure:
                                            try:
                                                parsed_delta = json.loads(delta)
                                                # 检查是否是车辆数据数组
                                                if isinstance(parsed_delta, list) and len(parsed_delta) > 0:
                                                    first_item = parsed_delta[0]
                                                    if isinstance(first_item, dict) and "id" in first_item:
                                                        is_special_data = True
                                                # 检查是否是任务数据
                                                elif isinstance(parsed_delta, dict) and "active_task_type" in parsed_delta:
                                                    is_special_data = True
                                            except json.JSONDecodeError:
                                                # 解析失败，不是特殊数据
                                                pass

                                    # 如果不是特殊数据，则发送给客户端
                                    if not is_special_data:
                                        yield f"{line}\n"
                                    else:
                                        # 是特殊数据，跳过不发送原始数据
                                        struct_logger.debug(
                                            "跳过原始特殊数据",
                                            delta_preview=delta[:100] if len(delta) > 100 else delta,
                                            conversation_id=request_data.conversation_id,
                                            message_id=message_id
                                        )
                            else:
                                # 没有choices的响应，直接发送
                                yield f"{line}\n"

                        except json.JSONDecodeError:
                            # 非JSON数据，直接转发
                            yield f"{line}\n"
                    else:
                        # 非data行，直接转发
                        yield f"{line}\n"

            # 如果还没有发送推荐数据，在最后发送
            if not sent_car_data and processed_car_data:
                for car_data in processed_car_data:
                    recommendation_response = {
                        "data": {
                            "user_id": user_uuid,
                            "message_id": message_id,
                            "conversation_id": request_data.conversation_id,
                            "choices": [{"delta": json.dumps(car_data, ensure_ascii=False)}],
                            "usage": {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
                        },
                        "status": {"code": 0, "message": "OK"}
                    }
                    yield f"data: {json.dumps(recommendation_response, ensure_ascii=False)}\n\n"

            if not sent_task_data and processed_task_data:
                task_response = {
                    "data": {
                        "user_id": user_uuid,
                        "message_id": message_id,
                        "conversation_id": request_data.conversation_id,
                        "choices": [{"delta": json.dumps(processed_task_data, ensure_ascii=False)}],
                        "usage": {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
                    },
                    "status": {"code": 0, "message": "OK"}
                }
                yield f"data: {json.dumps(task_response, ensure_ascii=False)}\n\n"

        async def generate_response():
            """新的三阶段响应生成流程"""
            try:
                # 阶段1：收集AI响应数据
                struct_logger.info(
                    "开始收集AI响应数据",
                    conversation_id=request_data.conversation_id,
                    message_id=message_id
                )

                ai_response = await collect_ai_response()

                struct_logger.info(
                    "AI响应数据收集完成",
                    conversation_id=request_data.conversation_id,
                    message_id=message_id,
                    content_length=len(ai_response["content"]),
                    car_data_count=len(ai_response["car_data"])
                )

                # 阶段2：处理数据并存储到数据库
                # 存储完整的文本内容到数据库
                struct_logger.info(
                    "准备保存AI响应内容到数据库",
                    conversation_id=request_data.conversation_id,
                    message_id=message_id,
                    content_length=len(ai_response["content"]),
                    content_preview=ai_response["content"][:200] if len(ai_response["content"]) > 200 else ai_response["content"],
                    content_full=ai_response["content"]  # 临时添加完整内容用于调试
                )
                
                chat_service.update_assistant_message(
                    assistant_message.uuid,
                    ai_response["content"],
                    is_complete=True
                )

                # 处理推荐数据
                processed_car_data = []
                processed_task_data = None

                if ai_response["car_data"] or ai_response["task_data"]:
                    await process_recommendation_data(
                        ai_response["car_data"],
                        ai_response["task_data"],
                        request_data.conversation_id,
                        message_id,
                        str(assistant_message.uuid)
                    )

                    # 准备处理后的推荐数据用于发送给客户端
                    if ai_response["car_data"]:
                        if ai_response["task_data"] and ai_response["task_data"].get("active_task_type") == "compare_car":
                            # 车辆对比：所有车辆作为一个对比组
                            processed_car_data.append(ai_response["car_data"])
                        else:
                            # 单车推荐：每辆车单独推荐
                            for car_data in ai_response["car_data"]:
                                processed_car_data.append([car_data])

                    processed_task_data = ai_response["task_data"]

                struct_logger.info(
                    "数据处理完成，开始发送响应给客户端",
                    conversation_id=request_data.conversation_id,
                    message_id=message_id,
                    processed_car_count=len(processed_car_data)
                )

                # 阶段3：重放响应给客户端
                async for chunk in replay_response_to_client(
                    ai_response["original_chunks"],
                    processed_car_data,
                    processed_task_data
                ):
                    yield chunk

            except Exception as e:
                struct_logger.error(
                    "生成流式响应失败",
                    user_id=user_id,
                    conversation_id=request_data.conversation_id,
                    error=str(e)
                )
                # 发送错误响应
                error_response = {
                    "data": {
                        "user_id": str(user_uuid),
                        "message_id": message_id,
                        "conversation_id": request_data.conversation_id,
                        "choices": [{"delta": "抱歉，服务暂时不可用，请稍后重试。"}],
                        "usage": {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
                    },
                    "status": {"code": 500, "message": "Internal Server Error"}
                }
                yield f"data: {json.dumps(error_response, ensure_ascii=False)}\n\n"
                yield "data: [DONE]\n\n"
        
        return StreamingResponse(
            generate_response(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Request-ID": request_id
            }
        )
        
    except Exception as e:
        struct_logger.error(
            "聊天完成请求失败",
            user_id=user_id,
            conversation_id=request_data.conversation_id,
            error=str(e),
            request_id=request_id
        )
        raise


@router.get(
    "/history/{conversation_id}",
    response_model=Dict[str, Any],
    tags=["聊天"],
    summary="获取对话历史",
    description="根据对话ID获取对话历史记录，支持分页和缓存"
)
async def get_conversation_history(
    conversation_id: str,
    request: Request,
    page: int = Query(1, ge=1, description="页码，从1开始"),
    limit: int = Query(50, ge=1, le=100, description="每页数量，最大100"),
    use_cache: bool = Query(True, description="是否使用缓存"),
    current_user: Dict[str, Any] = Depends(require_authentication),
    request_id: str = Depends(get_current_request_id)
):
    """
    获取对话历史（增强版）

    根据对话ID获取对话历史，包括用户消息、AI回复、相关推荐和对比数据。
    支持分页查询和缓存机制，提升查询性能。

    Args:
        conversation_id: 对话ID
        page: 页码，从1开始
        limit: 每页数量，最大100
        use_cache: 是否使用缓存
        request: FastAPI请求对象
        current_user: 当前认证用户信息
        request_id: 请求ID，用于日志追踪

    Returns:
        包含对话历史的响应，包括：
        - data: 对话历史列表
        - pagination: 分页信息
        - summary: 对话摘要信息

    Raises:
        AuthenticationError: 用户未认证
        NotFoundError: 对话不存在或不属于当前用户
        BusinessLogicError: 业务逻辑错误
    """
    try:
        # 获取用户ID
        user_id = current_user.get("user_id")

        # 获取对话历史
        history = chat_service.get_conversation_history(
            conversation_id, user_id, page, limit, use_cache
        )

        # 获取对话摘要
        summary = chat_service.get_conversation_summary(conversation_id, user_id)

        # 构建分页信息
        pagination = {
            "page": page,
            "limit": limit,
            "current_count": len(history),
            "has_more": len(history) == limit  # 如果返回数量等于限制，可能还有更多
        }

        struct_logger.info(
            "获取对话历史请求",
            user_id=user_id,
            conversation_id=conversation_id,
            page=page,
            limit=limit,
            use_cache=use_cache,
            history_count=len(history),
            request_id=request_id
        )

        return create_success_response(
            data={
                "history": history,
                "pagination": pagination,
                "summary": summary
            },
            message=f"查询成功，第{page}页共找到 {len(history)} 条消息",
            request_id=request_id
        )

    except Exception as e:
        struct_logger.error(
            "获取对话历史失败",
            user_id=current_user.get("user_id"),
            conversation_id=conversation_id,
            error=str(e),
            request_id=request_id
        )
        raise


@router.post(
    "/summary_topics",
    response_model=SuccessResponse[SummaryTopicsResponse],
    tags=["聊天"],
    summary="生成对话标题",
    description="根据用户消息生成对话标题并更新到对话记录中"
)
async def summary_topics(
    request_data: SummaryTopicsRequest,
    current_user: Dict[str, Any] = Depends(require_authentication),
    request_id: str = Depends(get_current_request_id)
):
    """
    生成对话标题接口

    根据用户的消息内容，调用AI服务生成合适的对话标题，并更新到对应的对话记录中。

    Args:
        request_data: 包含对话ID和用户消息的请求数据
        current_user: 当前认证用户信息
        request_id: 请求ID，用于日志追踪

    Returns:
        SummaryTopicsResponse: 包含对话ID和生成的标题

    Raises:
        HTTPException: 当对话不存在、无权限访问或生成失败时抛出
    """
    try:
        user_id = current_user.get("user_id")

        struct_logger.info(
            "开始生成对话标题",
            user_id=user_id,
            conversation_id=request_data.conversation_id,
            message=request_data.message,
            request_id=request_id
        )

        # 调用服务生成标题
        generated_title = await chat_service.generate_conversation_title(
            conversation_id=request_data.conversation_id,
            message=request_data.message,
            user_id=user_id
        )

        struct_logger.info(
            "对话标题生成成功",
            user_id=user_id,
            conversation_id=request_data.conversation_id,
            generated_title=generated_title,
            request_id=request_id
        )

        return create_success_response(
            data=SummaryTopicsResponse(
                conversation_id=request_data.conversation_id,
                title=generated_title
            ),
            message="对话标题生成成功",
            request_id=request_id
        )

    except Exception as e:
        struct_logger.error(
            "生成对话标题失败",
            user_id=current_user.get("user_id"),
            conversation_id=request_data.conversation_id,
            error=str(e),
            request_id=request_id
        )
        raise


# DELETE /cache/{conversation_id} 接口已移除
# 原因：该接口风险过高，可能导致缓存数据被恶意清除
# 移除时间：2025-07-24
# 如需缓存管理功能，请通过系统管理员接口或后台脚本实现


@router.get(
    "/recommendations/recent",
    response_model=Dict[str, Any],
    tags=["聊天"],
    summary="获取最近推荐",
    description="获取用户最近的车辆推荐记录"
)
async def get_recent_recommendations(
    limit: int = Query(10, ge=1, le=50, description="返回数量，最大50"),
    current_user: Dict[str, Any] = Depends(require_authentication),
    request_id: str = Depends(get_current_request_id)
):
    """
    获取最近推荐

    获取当前用户最近的车辆推荐记录，按时间倒序排列。

    Args:
        limit: 返回数量，最大50
        current_user: 当前认证用户信息
        request_id: 请求ID，用于日志追踪

    Returns:
        最近推荐列表响应
    """
    try:
        user_id = current_user.get("user_id")

        # 获取最近推荐
        recent_recommendations = chat_service.get_recent_recommendations(user_id, limit)

        struct_logger.info(
            "获取最近推荐请求",
            user_id=user_id,
            limit=limit,
            count=len(recent_recommendations),
            request_id=request_id
        )

        return create_success_response(
            data=recent_recommendations,
            message=f"查询成功，共找到 {len(recent_recommendations)} 条推荐记录",
            request_id=request_id
        )

    except Exception as e:
        struct_logger.error(
            "获取最近推荐失败",
            user_id=current_user.get("user_id"),
            limit=limit,
            error=str(e),
            request_id=request_id
        )
        raise


@router.get(
    "/summary/{conversation_id}",
    response_model=Dict[str, Any],
    tags=["聊天"],
    summary="获取对话摘要",
    description="获取对话的统计摘要信息"
)
async def get_conversation_summary(
    conversation_id: str,
    current_user: Dict[str, Any] = Depends(require_authentication),
    request_id: str = Depends(get_current_request_id)
):
    """
    获取对话摘要

    获取指定对话的统计摘要信息，包括消息数量、推荐数量、对比数量等。

    Args:
        conversation_id: 对话ID
        current_user: 当前认证用户信息
        request_id: 请求ID，用于日志追踪

    Returns:
        对话摘要响应
    """
    try:
        user_id = current_user.get("user_id")

        # 获取对话摘要
        summary = chat_service.get_conversation_summary(conversation_id, user_id)

        struct_logger.info(
            "获取对话摘要请求",
            user_id=user_id,
            conversation_id=conversation_id,
            summary=summary,
            request_id=request_id
        )

        return create_success_response(
            data=summary,
            message="获取对话摘要成功",
            request_id=request_id
        )

    except Exception as e:
        struct_logger.error(
            "获取对话摘要失败",
            user_id=current_user.get("user_id"),
            conversation_id=conversation_id,
            error=str(e),
            request_id=request_id
        )
        raise
