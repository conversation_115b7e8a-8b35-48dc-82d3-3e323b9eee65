"""
API v1 路由聚合
将所有 v1 版本的端点路由聚合到一个路由器中
"""

from fastapi import APIRouter

from app.api.v1.endpoints import health, auth, user, conversation, chat, car, tts
from app.config import settings

# 创建 API v1 路由器
api_router = APIRouter()

# 包含健康检查路由（已禁用）
# api_router.include_router(
#     health.router,
#     prefix="/health",
#     tags=["健康检查"]
# )

# 包含认证相关路由
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["认证"]
)

# 包含用户信息相关路由
api_router.include_router(
    user.router,
    prefix="/user",
    tags=["用户信息"]
)

# 包含AI对话会话相关路由
api_router.include_router(
    conversation.router,
    prefix="/conversations",
    tags=["AI对话会话"]
)

# 包含聊天相关路由
api_router.include_router(
    chat.router,
    prefix="/chat",
    tags=["聊天"]
)

api_router.include_router(
    car.router,
    prefix="/car",
    tags=["汽车服务"]
)

api_router.include_router(
    tts.router,
    prefix="/tts",
    tags=["TTS服务"]
)

# 可以在这里添加更多的路由模块
# 例如：
# api_router.include_router(
#     orders.router,
#     prefix="/orders",
#     tags=["订单"]
# )

# api_router.include_router(
#     notifications.router,
#     prefix="/notifications",
#     tags=["通知"]
# )

# api_router.include_router(
#     files.router,
#     prefix="/files",
#     tags=["文件管理"]
# )

# 根路径信息端点
@api_router.get("/", tags=["API信息"])
async def api_info():
    """
    API v1 信息
    返回 API 版本和可用端点信息
    """
    return {
        "message": f"欢迎使用 {settings.app_name} API v1",
        "version": "1.0.0",
        "app_version": settings.app_version,
        "environment": settings.environment,
        "endpoints": {
            # "health": {
            #     "description": "健康检查和监控（已禁用）",
            #     "paths": [
            #         "/health/",
            #         "/health/detailed",
            #         "/health/metrics",
            #         "/health/readiness",
            #         "/health/liveness"
            #     ]
            # },
            "auth": {
                "description": "用户认证",
                "paths": [
                    "/auth/send-code",
                    "/auth/login",
                    "/auth/refresh",
                    "/auth/logout"
                ]
            },
            "user": {
                "description": "用户信息管理",
                "paths": [
                    "/user/profile",
                    "/user/username",
                    "/user/avatar",
                    "/user/avatar/{file_name}",
                    "/user/address",
                    "/user/provinces",
                    "/user/cities/{province_code}"
                ]
            },
            "conversations": {
                "description": "AI对话会话管理",
                "paths": [
                    "/conversations/",
                    "/conversations/{conversation_id}",
                    "/conversations/delete"
                ]
            },
            "chat": {
                "description": "聊天功能",
                "paths": [
                    "/chat/completions",
                    "/chat/history/{conversation_id}",
                    "/chat/summary_topics"
                ]
            },
            "car": {
                "description": "汽车服务",
                "paths": [
                    "/car/asr",
                    "/car/vehicle_cars/{uuid}/pictures",
                    "/car/vehicle_cars/{uuid}/damage"
                ]
            },
            "tts": {
                "description": "文本转语音服务",
                "paths": [
                    "/tts/"
                ]
            }
        },
        "documentation": {
            "swagger_ui": f"{settings.docs_url}" if settings.docs_url else "disabled",
            "redoc": f"{settings.redoc_url}" if settings.redoc_url else "disabled"
        }
    }
