"""
用户相关数据模型
"""

from sqlalchemy import Column, String, Boolean, DateTime, Text, Integer, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid
from datetime import datetime
from typing import Dict, Any

from app.database import Base


class User(Base):
    """用户模型"""

    __tablename__ = "users"

    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    uuid = Column(UUID(as_uuid=True), unique=True, default=uuid.uuid4, index=True, nullable=False, comment="UUID")

    # 基本信息
    phone = Column(String(20), unique=True, index=True, nullable=False, comment="手机号")
    username = Column(String(50), comment="用户名")
    avatar_url = Column(String(500), comment="头像URL")

    # 地址信息
    province_code = Column(String(10), comment="省份代码")
    city_code = Column(String(10), comment="城市代码")

    # 状态信息
    is_active = Column(Boolean, default=True, nullable=False, comment="是否激活")
    is_deleted = Column(Boolean, default=False, nullable=False, comment="是否删除")

    # 时间信息
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")
    deleted_at = Column(DateTime(timezone=True), comment="删除时间")
    last_login = Column(DateTime(timezone=True), comment="最后登录时间")

    def __repr__(self):
        return f"<User(id={self.id}, uuid={self.uuid}, phone={self.phone}, username={self.username})>"

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "uuid": str(self.uuid),
            "phone": self.phone,
            "username": self.username,
            "avatar_url": self.avatar_url,
            "province_code": self.province_code,
            "city_code": self.city_code,
            "is_active": self.is_active,
            "is_deleted": self.is_deleted,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "deleted_at": self.deleted_at.isoformat() if self.deleted_at else None,
            "last_login": self.last_login.isoformat() if self.last_login else None
        }

    def update_last_login(self):
        """更新最后登录时间"""
        from app.utils.timezone import to_utc_for_db
        self.last_login = to_utc_for_db()

    def soft_delete(self):
        """软删除"""
        from app.utils.timezone import to_utc_for_db
        self.is_deleted = True
        self.deleted_at = to_utc_for_db()

    def restore(self):
        """恢复删除"""
        self.is_deleted = False
        self.deleted_at = None


class SMSCode(Base):
    """短信验证码模型"""

    __tablename__ = "sms_codes"

    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    uuid = Column(UUID(as_uuid=True), unique=True, default=uuid.uuid4, index=True, nullable=False, comment="UUID")

    # 基本信息
    phone = Column(String(20), nullable=False, index=True, comment="手机号")
    code = Column(String(6), nullable=False, comment="验证码")
    purpose = Column(String(20), nullable=False, comment="用途")

    # 状态信息
    is_used = Column(Boolean, default=False, nullable=False, comment="是否已使用")
    is_deleted = Column(Boolean, default=False, nullable=False, comment="是否删除")

    # 时间信息
    expires_at = Column(DateTime(timezone=True), nullable=False, comment="过期时间")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")
    deleted_at = Column(DateTime(timezone=True), comment="删除时间")

    def __repr__(self):
        return f"<SMSCode(id={self.id}, uuid={self.uuid}, phone={self.phone}, purpose={self.purpose})>"

    def is_expired(self) -> bool:
        """检查是否过期"""
        from app.utils.timezone import is_expired
        return is_expired(self.expires_at)

    def mark_as_used(self):
        """标记为已使用"""
        self.is_used = True

    def soft_delete(self):
        """软删除"""
        from app.utils.timezone import to_utc_for_db
        self.is_deleted = True
        self.deleted_at = to_utc_for_db()


class Province(Base):
    """省份模型"""

    __tablename__ = "provinces"

    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    uuid = Column(UUID(as_uuid=True), unique=True, default=uuid.uuid4, index=True, nullable=False, comment="UUID")

    # 基本信息
    code = Column(String(10), unique=True, nullable=False, index=True, comment="省份代码")
    name = Column(String(50), nullable=False, comment="省份名称")

    # 状态信息
    is_active = Column(Boolean, default=True, nullable=False, comment="是否激活")
    is_deleted = Column(Boolean, default=False, nullable=False, comment="是否删除")

    # 时间信息
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")
    deleted_at = Column(DateTime(timezone=True), comment="删除时间")

    def __repr__(self):
        return f"<Province(id={self.id}, uuid={self.uuid}, code={self.code}, name={self.name})>"

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "uuid": str(self.uuid),
            "code": self.code,
            "name": self.name,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

    def soft_delete(self):
        """软删除"""
        from app.utils.timezone import to_utc_for_db
        self.is_deleted = True
        self.deleted_at = to_utc_for_db()


class City(Base):
    """城市模型"""

    __tablename__ = "cities"

    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    uuid = Column(UUID(as_uuid=True), unique=True, default=uuid.uuid4, index=True, nullable=False, comment="UUID")

    # 基本信息
    code = Column(String(10), unique=True, nullable=False, index=True, comment="城市代码")
    name = Column(String(50), nullable=False, comment="城市名称")
    province_code = Column(String(10), nullable=False, index=True, comment="所属省份代码")

    # 状态信息
    is_active = Column(Boolean, default=True, nullable=False, comment="是否激活")
    is_deleted = Column(Boolean, default=False, nullable=False, comment="是否删除")

    # 时间信息
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")
    deleted_at = Column(DateTime(timezone=True), comment="删除时间")

    def __repr__(self):
        return f"<City(id={self.id}, uuid={self.uuid}, code={self.code}, name={self.name}, province_code={self.province_code})>"

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "uuid": str(self.uuid),
            "code": self.code,
            "name": self.name,
            "province_code": self.province_code,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }

    def soft_delete(self):
        """软删除"""
        from app.utils.timezone import to_utc_for_db
        self.is_deleted = True
        self.deleted_at = to_utc_for_db()


class UserToken(Base):
    """用户令牌模型"""

    __tablename__ = "user_tokens"

    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    uuid = Column(UUID(as_uuid=True), unique=True, default=uuid.uuid4, index=True, nullable=False, comment="UUID")

    # 关联用户
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, index=True, comment="用户ID")

    # 令牌信息
    access_token_jti = Column(String(36), unique=True, nullable=False, index=True, comment="访问令牌JTI")
    refresh_token_jti = Column(String(36), unique=True, nullable=False, index=True, comment="刷新令牌JTI")

    # 设备信息
    device_id = Column(String(100), comment="设备ID")
    device_name = Column(String(200), comment="设备名称")
    client_ip = Column(String(45), comment="客户端IP")
    user_agent = Column(Text, comment="用户代理")

    # 状态信息
    is_active = Column(Boolean, default=True, nullable=False, comment="是否激活")
    is_deleted = Column(Boolean, default=False, nullable=False, comment="是否删除")

    # 时间信息
    access_token_expires_at = Column(DateTime(timezone=True), nullable=False, comment="访问令牌过期时间")
    refresh_token_expires_at = Column(DateTime(timezone=True), nullable=False, comment="刷新令牌过期时间")
    last_used_at = Column(DateTime(timezone=True), comment="最后使用时间")
    created_at = Column(DateTime(timezone=True), nullable=False, comment="创建时间")
    updated_at = Column(DateTime(timezone=True), comment="更新时间")
    deleted_at = Column(DateTime(timezone=True), comment="删除时间")

    # 关系
    user = relationship("User", backref="tokens")

    def __repr__(self):
        return f"<UserToken(id={self.id}, user_id={self.user_id}, device_id={self.device_id})>"

    def is_access_token_expired(self) -> bool:
        """检查访问令牌是否过期"""
        from app.utils.timezone import is_expired
        return is_expired(self.access_token_expires_at)

    def is_refresh_token_expired(self) -> bool:
        """检查刷新令牌是否过期"""
        from app.utils.timezone import is_expired
        return is_expired(self.refresh_token_expires_at)

    def update_last_used(self):
        """更新最后使用时间"""
        from app.utils.timezone import to_utc_for_db
        self.last_used_at = to_utc_for_db()
        self.updated_at = to_utc_for_db()

    def update_timestamps(self):
        """更新时间戳"""
        from app.utils.timezone import to_utc_for_db
        self.updated_at = to_utc_for_db()

    def revoke(self):
        """撤销令牌"""
        from app.utils.timezone import to_utc_for_db
        self.is_active = False
        self.updated_at = to_utc_for_db()

    def soft_delete(self):
        """软删除"""
        from app.utils.timezone import to_utc_for_db
        self.is_deleted = True
        self.deleted_at = to_utc_for_db()
        self.updated_at = to_utc_for_db()
        self.is_active = False

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "uuid": str(self.uuid),
            "user_id": self.user_id,
            "device_id": self.device_id,
            "device_name": self.device_name,
            "client_ip": self.client_ip,
            "is_active": self.is_active,
            "last_used_at": self.last_used_at.isoformat() if self.last_used_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
