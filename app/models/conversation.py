"""
AI对话会话相关数据模型
"""

from sqlalchemy import Column, String, Boolean, DateTime, Text, Integer, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid
from datetime import datetime
from typing import Dict, Any

from app.database import Base


class AIConversation(Base):
    """AI对话会话模型"""

    __tablename__ = "ai_conversations"

    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    uuid = Column(UUID(as_uuid=True), unique=True, default=uuid.uuid4, index=True, nullable=False, comment="会话UUID")

    # 关联用户
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, index=True, comment="用户ID")

    # 会话信息
    title = Column(String(200), nullable=False, comment="会话标题")

    # 状态信息
    is_deleted = Column(<PERSON>olean, default=False, nullable=False, comment="是否删除")

    # 时间信息
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False, comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")
    deleted_at = Column(DateTime(timezone=True), comment="删除时间")

    # 关系
    user = relationship("User", backref="ai_conversations")

    def __repr__(self):
        return f"<AIConversation(id={self.id}, uuid={self.uuid}, user_id={self.user_id}, title={self.title})>"

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "conversation_id": str(self.uuid),  # 使用conversation_id作为对外字段名
            "user_id": self.user_id,
            "title": self.title,
            "is_deleted": self.is_deleted,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "deleted_at": self.deleted_at.isoformat() if self.deleted_at else None
        }

    def soft_delete(self):
        """软删除"""
        from app.utils.timezone import to_utc_for_db
        self.is_deleted = True
        self.deleted_at = to_utc_for_db()

    def restore(self):
        """恢复删除"""
        self.is_deleted = False
        self.deleted_at = None
