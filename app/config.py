"""
应用配置管理模块
使用 Pydantic Settings 管理环境变量和配置
"""

from typing import List, Optional
from pydantic import Field, ConfigDict
from pydantic_settings import BaseSettings
from functools import lru_cache
import os


class Settings(BaseSettings):
    """应用设置类"""
    
    # 应用基本信息
    app_name: str = Field(default="FastAPI Server", env="APP_NAME")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    app_description: str = Field(default="基于 FastAPI 的现代化 Web 服务，提供用户认证、信息管理、健康检查等功能", env="APP_DESCRIPTION")
    debug: bool = Field(default=False, env="DEBUG")
    environment: str = Field(default="production", env="ENVIRONMENT")
    
    # 服务器配置
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    reload: bool = Field(default=False, env="RELOAD")
    
    # 日志配置
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_format: str = Field(default="json", env="LOG_FORMAT")  # json 或 text
    log_file_enabled: bool = Field(default=True, env="LOG_FILE_ENABLED")
    log_console_enabled: bool = Field(default=False, env="LOG_CONSOLE_ENABLED")  # 默认禁用控制台输出
    log_max_size: int = Field(default=10485760, env="LOG_MAX_SIZE")  # 10MB
    log_backup_count: int = Field(default=5, env="LOG_BACKUP_COUNT")
    
    # 安全配置
    secret_key: str = Field(default="change-this-secret-key", env="SECRET_KEY")
    algorithm: str = Field(default="HS256", env="ALGORITHM")
    access_token_expire_minutes: int = Field(default=43200, env="ACCESS_TOKEN_EXPIRE_MINUTES")  # 30天
    
    # 数据库配置
    database_url: str = Field(default="postgresql://username:password@localhost:5432/fastapi_db", env="DATABASE_URL")
    database_host: str = Field(default="localhost", env="DATABASE_HOST")
    database_port: int = Field(default=5432, env="DATABASE_PORT")
    database_name: str = Field(default="fastapi_db", env="DATABASE_NAME")
    database_user: str = Field(default="username", env="DATABASE_USER")
    database_password: str = Field(default="password", env="DATABASE_PASSWORD")
    
    # CORS 配置
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"], 
        env="CORS_ORIGINS"
    )
    cors_allow_credentials: bool = Field(default=True, env="CORS_ALLOW_CREDENTIALS")
    cors_allow_methods: List[str] = Field(default=["*"], env="CORS_ALLOW_METHODS")
    cors_allow_headers: List[str] = Field(default=["*"], env="CORS_ALLOW_HEADERS")
    
    # API 配置
    api_v1_prefix: str = Field(default="/api/v1", env="API_V1_PREFIX")
    docs_url: Optional[str] = Field(default="/docs", env="DOCS_URL")
    redoc_url: Optional[str] = Field(default="/redoc", env="REDOC_URL")
    
    # 监控配置
    metrics_enabled: bool = Field(default=True, env="METRICS_ENABLED")
    health_check_enabled: bool = Field(default=True, env="HEALTH_CHECK_ENABLED")
    
    model_config = ConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False
    )
    
    @property
    def is_development(self) -> bool:
        """判断是否为开发环境"""
        return self.environment.lower() in ["development", "dev", "local"]
    
    @property
    def is_production(self) -> bool:
        """判断是否为生产环境"""
        return self.environment.lower() in ["production", "prod"]
    
    @property
    def log_dir(self) -> str:
        """日志目录路径"""
        return os.path.join(os.getcwd(), "logs")
    
    @property
    def log_file_path(self) -> str:
        """日志文件路径"""
        return os.path.join(self.log_dir, f"{self.app_name.lower().replace(' ', '_')}.log")


@lru_cache()
def get_settings() -> Settings:
    """获取应用设置实例（单例模式）"""
    return Settings()


# 全局设置实例
settings = get_settings()
