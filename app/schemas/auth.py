"""
认证相关的 Pydantic 模型
"""

from typing import Optional
from pydantic import BaseModel, Field, field_validator
import re


class SendCodeRequest(BaseModel):
    """发送验证码请求"""
    
    phone: str = Field(..., description="手机号")
    purpose: str = Field(default="login", description="用途")
    
    @field_validator('phone')
    @classmethod
    def validate_phone(cls, v):
        """验证手机号格式"""
        # 支持新格式：+8613333333333
        if not re.match(r'^\+861[3-9]\d{9}$', v):
            raise ValueError('手机号格式不正确，应为+86开头的中国手机号')
        return v

    @field_validator('purpose')
    @classmethod
    def validate_purpose(cls, v):
        """验证用途"""
        allowed_purposes = ['login', 'register', 'reset_password']
        if v not in allowed_purposes:
            raise ValueError(f'用途必须是以下之一: {", ".join(allowed_purposes)}')
        return v


class SendCodeResponse(BaseModel):
    """发送验证码响应"""
    
    success: bool = Field(description="是否成功")
    message: str = Field(description="响应消息")
    code: Optional[str] = Field(default=None, description="验证码（仅测试环境）")
    expires_in: int = Field(description="过期时间（秒）")


class LoginRequest(BaseModel):
    """登录请求"""
    
    phone: str = Field(..., description="手机号")
    code: str = Field(..., description="验证码")
    
    @field_validator('phone')
    @classmethod
    def validate_phone(cls, v):
        """验证手机号格式"""
        # 支持新格式：+8613333333333
        if not re.match(r'^\+861[3-9]\d{9}$', v):
            raise ValueError('手机号格式不正确，应为+86开头的中国手机号')
        return v

    @field_validator('code')
    @classmethod
    def validate_code(cls, v):
        """验证验证码格式"""
        if not re.match(r'^\d{6}$', v):
            raise ValueError('验证码必须是6位数字')
        return v


class LoginResponse(BaseModel):
    """登录响应"""
    
    success: bool = Field(description="是否成功")
    message: str = Field(description="响应消息")
    access_token: Optional[str] = Field(default=None, description="访问令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    expires_in: Optional[int] = Field(default=None, description="令牌过期时间（秒）")
    user_info: Optional[dict] = Field(default=None, description="用户信息")


class TokenPayload(BaseModel):
    """JWT 令牌载荷"""

    sub: str = Field(description="用户UUID")
    phone: str = Field(description="手机号")
    exp: int = Field(description="过期时间戳")
    iat: int = Field(description="签发时间戳")
