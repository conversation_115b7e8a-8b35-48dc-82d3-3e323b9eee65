"""
通用响应模型
定义标准化的API响应格式
"""

from typing import Any, Dict, Generic, List, Optional, TypeVar
from pydantic import BaseModel, Field, ConfigDict
from datetime import datetime
from app.utils.timezone import get_china_now


DataT = TypeVar('DataT')


class BaseResponse(BaseModel, Generic[DataT]):
    """基础响应模型"""
    
    success: bool = Field(description="请求是否成功")
    message: str = Field(description="响应消息")
    data: Optional[DataT] = Field(default=None, description="响应数据")
    timestamp: datetime = Field(default_factory=get_china_now, description="响应时间")
    request_id: Optional[str] = Field(default=None, description="请求ID")
    
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class SuccessResponse(BaseResponse[DataT]):
    """成功响应模型"""
    
    success: bool = Field(default=True, description="请求成功")
    message: str = Field(default="操作成功", description="成功消息")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    
    success: bool = Field(default=False, description="请求失败")
    error: Dict[str, Any] = Field(description="错误信息")
    timestamp: datetime = Field(default_factory=get_china_now, description="响应时间")
    
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class PaginationMeta(BaseModel):
    """分页元数据"""
    
    page: int = Field(description="当前页码")
    size: int = Field(description="每页大小")
    total: int = Field(description="总记录数")
    pages: int = Field(description="总页数")
    has_next: bool = Field(description="是否有下一页")
    has_prev: bool = Field(description="是否有上一页")


class PaginatedResponse(BaseModel, Generic[DataT]):
    """分页响应模型"""
    
    success: bool = Field(default=True, description="请求是否成功")
    message: str = Field(default="查询成功", description="响应消息")
    data: List[DataT] = Field(description="数据列表")
    meta: PaginationMeta = Field(description="分页信息")
    timestamp: datetime = Field(default_factory=get_china_now, description="响应时间")
    request_id: Optional[str] = Field(default=None, description="请求ID")
    
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class HealthCheckResponse(BaseModel):
    """健康检查响应"""
    
    status: str = Field(description="服务状态")
    app_name: str = Field(description="应用名称")
    version: str = Field(description="应用版本")
    environment: str = Field(description="运行环境")
    timestamp: float = Field(description="时间戳")
    uptime: str = Field(description="运行时间")
    database: str = Field(description="数据库状态")
    cache: str = Field(description="缓存状态")


class MetricsResponse(BaseModel):
    """指标响应"""
    
    requests_total: str = Field(description="总请求数")
    requests_duration: str = Field(description="请求持续时间")
    active_connections: str = Field(description="活跃连接数")
    memory_usage: str = Field(description="内存使用情况")


# 响应工厂函数
def create_success_response(
    data: Any = None,
    message: str = "操作成功",
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """创建成功响应"""
    return {
        "success": True,
        "message": message,
        "data": data,
        "timestamp": get_china_now().isoformat(),
        "request_id": request_id
    }


def create_error_response(
    message: str,
    error_type: str = "Error",
    details: Optional[Dict[str, Any]] = None,
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """创建错误响应"""
    return {
        "success": False,
        "error": {
            "type": error_type,
            "message": message,
            "details": details or {},
            "request_id": request_id
        },
        "timestamp": get_china_now().isoformat()
    }


def create_paginated_response(
    data: List[Any],
    page: int,
    size: int,
    total: int,
    message: str = "查询成功",
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """创建分页响应"""
    pages = (total + size - 1) // size  # 向上取整
    
    return {
        "success": True,
        "message": message,
        "data": data,
        "meta": {
            "page": page,
            "size": size,
            "total": total,
            "pages": pages,
            "has_next": page < pages,
            "has_prev": page > 1
        },
        "timestamp": get_china_now().isoformat(),
        "request_id": request_id
    }
