"""
TTS（文本转语音）相关的数据模型
"""

from typing import Any, Dict
from pydantic import BaseModel, Field, ConfigDict


class TTSRequest(BaseModel):
    """TTS请求模型"""
    
    message: str = Field(..., min_length=1, max_length=5000, description="要转换为语音的文本内容")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "message": "你好，欢迎使用文本转语音服务！"
            }
        }
    )


class TTSResponse(BaseModel):
    """TTS响应模型"""
    
    data: Dict[str, Any] = Field(description="TTS服务返回的原始数据")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "data": {
                    "code": 0,
                    "message": "success",
                    "audio_url": "https://example.com/audio.mp3"
                }
            }
        }
    )
