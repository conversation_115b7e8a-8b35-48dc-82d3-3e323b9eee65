"""
聊天相关的数据模型
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, ConfigDict
from datetime import datetime


class ChatCompletionRequest(BaseModel):
    """聊天完成请求模型"""

    message: str = Field(..., min_length=1, max_length=2000, description="聊天内容")
    conversation_id: str = Field(..., description="对话ID")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "message": "帮我找一辆贰拾万元左右的奥迪 suv，年限、颜色、表显里程都无所谓！",
                "conversation_id": "550e8400-e29b-41d4-a716-446655440000"
            }
        }
    )


class ChatChoice(BaseModel):
    """聊天选择模型"""
    
    delta: str = Field(description="增量内容")
    finish_reason: Optional[str] = Field(default=None, description="完成原因")


class ChatUsage(BaseModel):
    """聊天使用统计模型"""
    
    prompt_tokens: int = Field(description="提示词令牌数")
    completion_tokens: int = Field(description="完成令牌数")
    total_tokens: int = Field(description="总令牌数")


class ChatCompletionData(BaseModel):
    """聊天完成数据模型"""
    
    user_id: str = Field(description="用户ID")
    message_id: str = Field(description="消息ID")
    conversation_id: str = Field(description="对话ID")
    choices: List[ChatChoice] = Field(description="选择列表")
    usage: Optional[ChatUsage] = Field(default=None, description="使用统计")


class ChatCompletionStatus(BaseModel):
    """聊天完成状态模型"""
    
    code: int = Field(description="状态码")
    message: str = Field(description="状态消息")


class ChatCompletionResponse(BaseModel):
    """聊天完成响应模型"""
    
    data: ChatCompletionData = Field(description="响应数据")
    status: ChatCompletionStatus = Field(description="响应状态")


class ChatMessageInfo(BaseModel):
    """聊天消息信息模型"""
    
    id: int = Field(description="消息ID")
    message_id: str = Field(description="消息UUID")
    conversation_id: str = Field(description="对话ID")
    user_id: int = Field(description="用户ID")
    message_type: str = Field(description="消息类型")
    content: str = Field(description="消息内容")
    is_complete: bool = Field(description="是否完整")
    created_at: str = Field(description="创建时间")
    updated_at: Optional[str] = Field(default=None, description="更新时间")


class CarRecommendationInfo(BaseModel):
    """车辆推荐信息模型"""

    id: str = Field(description="推荐UUID")
    conversation_id: str = Field(description="对话ID")
    message_id: str = Field(description="消息ID")
    task_type: str = Field(description="任务类型")
    recommendation_data: Optional[Any] = Field(default=None, description="推荐数据（支持列表或字典格式）")
    slots_data: Optional[Dict[str, Any]] = Field(default=None, description="槽位数据")
    created_at: str = Field(description="创建时间")


class CarComparisonInfo(BaseModel):
    """车辆对比信息模型"""

    id: str = Field(description="对比UUID")
    conversation_id: str = Field(description="对话ID")
    message_id: str = Field(description="消息ID")
    task_type: str = Field(description="任务类型")
    car_1_id: str = Field(description="第一辆车UUID")
    car_2_id: str = Field(description="第二辆车UUID")
    comparison_data: Optional[Any] = Field(default=None, description="对比数据（支持列表或字典格式）")
    slots_data: Optional[Dict[str, Any]] = Field(default=None, description="槽位数据")
    created_at: str = Field(description="创建时间")


class ConversationHistoryItem(BaseModel):
    """对话历史项模型"""

    message: ChatMessageInfo = Field(description="消息信息")
    recommendations: List[CarRecommendationInfo] = Field(default=[], description="相关推荐")
    comparisons: List[Dict[str, Any]] = Field(default=[], description="相关对比")


class ConversationHistoryResponse(BaseModel):
    """对话历史响应模型"""
    
    success: bool = Field(default=True, description="是否成功")
    message: str = Field(default="查询成功", description="响应消息")
    data: List[ConversationHistoryItem] = Field(description="对话历史")
    timestamp: str = Field(description="响应时间")
    request_id: Optional[str] = Field(default=None, description="请求ID")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "success": True,
                "message": "查询成功",
                "data": [
                    {
                        "message": {
                            "id": 1,
                            "message_id": "550e8400-e29b-41d4-a716-446655440000",
                            "conversation_id": "b3c80499-ac4f-4e16-bd98-20e04b66536d",
                            "user_id": 123,
                            "message_type": "user",
                            "content": "我想了解奥迪Q5",
                            "is_complete": True,
                            "created_at": "2024-01-01T10:00:00+08:00"
                        },
                        "recommendations": []
                    }
                ],
                "timestamp": "2024-01-01T10:00:00+08:00",
                "request_id": "req_123456"
            }
        }
    )


class StreamDataModel(BaseModel):
    """流式数据模型"""
    
    data: Dict[str, Any] = Field(description="数据内容")
    
    def to_sse_format(self) -> str:
        """转换为SSE格式"""
        import json
        return f"data: {json.dumps(self.data, ensure_ascii=False)}\n\n"


class SummaryTopicsRequest(BaseModel):
    """话题摘要请求模型"""

    conversation_id: str = Field(..., description="对话ID")
    message: str = Field(..., min_length=1, max_length=2000, description="用户消息")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "conversation_id": "550e8400-e29b-41d4-a716-446655440000",
                "message": "帮我找辆五十万的车！"
            }
        }
    )


class SummaryTopicsResponse(BaseModel):
    """话题摘要响应模型"""

    conversation_id: str = Field(description="对话ID")
    title: str = Field(description="生成的对话标题")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "conversation_id": "550e8400-e29b-41d4-a716-446655440000",
                "title": "找五十万好车"
            }
        }
    )


class ASRResponse(BaseModel):
    """ASR语音识别响应模型"""

    # 这里使用Any类型，因为我们要原样返回外部API的响应
    data: Dict[str, Any] = Field(description="ASR识别结果数据")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "data": {
                    "text": "帮我找辆五十万的车",
                    "confidence": 0.95,
                    "duration": 2.5
                }
            }
        }
    )
