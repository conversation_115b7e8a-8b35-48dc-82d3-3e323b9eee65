"""
用户相关的 Pydantic 模型
"""

from typing import Optional
from pydantic import BaseModel, Field, field_validator, ConfigDict
from datetime import datetime
import re


class UserProfile(BaseModel):
    """用户资料"""

    id: int = Field(description="主键ID")
    uuid: str = Field(description="UUID")
    phone: str = Field(description="手机号")
    username: Optional[str] = Field(default=None, description="用户名")
    avatar_url: Optional[str] = Field(default=None, description="头像URL")
    province_code: Optional[str] = Field(default=None, description="省份代码")
    province_name: Optional[str] = Field(default=None, description="省份名称")
    city_code: Optional[str] = Field(default=None, description="城市代码")
    city_name: Optional[str] = Field(default=None, description="城市名称")
    is_active: bool = Field(description="是否激活")
    created_at: Optional[datetime] = Field(default=None, description="创建时间")
    last_login: Optional[datetime] = Field(default=None, description="最后登录时间")
    
    model_config = ConfigDict(
        from_attributes=True,
        json_encoders={
            datetime: lambda v: v.isoformat() if v else None
        }
    )


class UpdateUsernameRequest(BaseModel):
    """更新用户名请求"""
    
    username: str = Field(..., min_length=2, max_length=20, description="用户名")
    
    @field_validator('username')
    @classmethod
    def validate_username(cls, v):
        """验证用户名格式"""
        # 只允许中文、英文、数字和下划线
        if not re.match(r'^[\u4e00-\u9fa5a-zA-Z0-9_]+$', v):
            raise ValueError('用户名只能包含中文、英文、数字和下划线')
        return v


class UpdateAddressRequest(BaseModel):
    """更新地址请求"""
    
    province_code: str = Field(..., description="省份代码")
    city_code: str = Field(..., description="城市代码")
    
    @field_validator('province_code')
    @classmethod
    def validate_province_code(cls, v):
        """验证省份代码格式"""
        if not re.match(r'^\d{2}$', v):
            raise ValueError('省份代码格式不正确')
        return v

    @field_validator('city_code')
    @classmethod
    def validate_city_code(cls, v):
        """验证城市代码格式"""
        if not re.match(r'^\d{4}$', v):
            raise ValueError('城市代码格式不正确')
        return v


class UploadAvatarResponse(BaseModel):
    """上传头像响应"""
    
    success: bool = Field(description="是否成功")
    message: str = Field(description="响应消息")
    avatar_url: Optional[str] = Field(default=None, description="头像URL")


class Province(BaseModel):
    """省份信息"""

    id: int = Field(description="主键ID")
    uuid: str = Field(description="UUID")
    code: str = Field(description="省份代码")
    name: str = Field(description="省份名称")
    is_active: bool = Field(description="是否激活")
    created_at: Optional[datetime] = Field(default=None, description="创建时间")

    model_config = ConfigDict(from_attributes=True)


class City(BaseModel):
    """城市信息"""

    id: int = Field(description="主键ID")
    uuid: str = Field(description="UUID")
    code: str = Field(description="城市代码")
    name: str = Field(description="城市名称")
    province_code: str = Field(description="所属省份代码")
    is_active: bool = Field(description="是否激活")
    created_at: Optional[datetime] = Field(default=None, description="创建时间")

    model_config = ConfigDict(from_attributes=True)
