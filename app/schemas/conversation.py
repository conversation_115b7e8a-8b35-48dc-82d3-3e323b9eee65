"""
AI对话会话相关的数据模型
"""

from typing import List, Optional
from pydantic import BaseModel, Field, ConfigDict
from datetime import datetime


class CreateConversationRequest(BaseModel):
    """创建会话请求模型"""

    title: str = Field(..., min_length=1, max_length=200, description="会话标题")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "title": "帮我找一辆贰拾万元左右的奥迪 suv，年限、颜色、表显里程都无所谓！"
            }
        }
    )


class DeleteConversationRequest(BaseModel):
    """删除会话请求模型"""

    conversation_id: str = Field(..., description="要删除的会话ID（UUID）")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "conversation_id": "550e8400-e29b-41d4-a716-************"
            }
        }
    )


class ConversationInfo(BaseModel):
    """会话信息模型"""
    
    id: int = Field(description="会话ID")
    conversation_id: str = Field(description="会话UUID")
    user_id: int = Field(description="用户ID")
    title: str = Field(description="会话标题")
    is_deleted: bool = Field(description="是否删除")
    created_at: str = Field(description="创建时间")
    updated_at: Optional[str] = Field(default=None, description="更新时间")
    deleted_at: Optional[str] = Field(default=None, description="删除时间")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "id": 1,
                "conversation_id": "550e8400-e29b-41d4-a716-************",
                "user_id": 123,
                "title": "帮我找一辆贰拾万元左右的奥迪 suv，年限、颜色、表显里程都无所谓！",
                "is_deleted": False,
                "created_at": "2024-01-01T10:00:00+08:00",
                "updated_at": "2024-01-01T10:30:00+08:00",
                "deleted_at": None
            }
        }
    )


class CreateConversationResponse(BaseModel):
    """创建会话响应模型"""
    
    success: bool = Field(default=True, description="是否成功")
    message: str = Field(default="会话创建成功", description="响应消息")
    data: ConversationInfo = Field(description="会话信息")
    timestamp: str = Field(description="响应时间")
    request_id: Optional[str] = Field(default=None, description="请求ID")


class ConversationListItem(BaseModel):
    """会话列表项模型"""
    
    id: int = Field(description="会话ID")
    conversation_id: str = Field(description="会话UUID")
    title: str = Field(description="会话标题")
    created_at: str = Field(description="创建时间")
    updated_at: Optional[str] = Field(default=None, description="更新时间")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "id": 1,
                "conversation_id": "550e8400-e29b-41d4-a716-************",
                "title": "帮我找一辆贰拾万元左右的奥迪 suv，年限、颜色、表显里程都无所谓！",
                "created_at": "2024-01-01T10:00:00+08:00",
                "updated_at": "2024-01-01T10:30:00+08:00"
            }
        }
    )


class GetConversationsResponse(BaseModel):
    """获取会话列表响应模型"""
    
    success: bool = Field(default=True, description="是否成功")
    message: str = Field(default="查询成功", description="响应消息")
    data: List[ConversationListItem] = Field(description="会话列表")
    timestamp: str = Field(description="响应时间")
    request_id: Optional[str] = Field(default=None, description="请求ID")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "success": True,
                "message": "查询成功",
                "data": [
                    {
                        "id": 1,
                        "conversation_id": "550e8400-e29b-41d4-a716-************",
                        "title": "帮我找一辆贰拾万元左右的奥迪 suv，年限、颜色、表显里程都无所谓！",
                        "created_at": "2024-01-01T10:00:00+08:00",
                        "updated_at": "2024-01-01T10:30:00+08:00"
                    }
                ],
                "timestamp": "2024-01-01T10:00:00+08:00",
                "request_id": "req_123456"
            }
        }
    )
