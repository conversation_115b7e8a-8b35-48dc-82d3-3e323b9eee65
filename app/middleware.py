"""
FastAPI 中间件模块
包含请求日志、请求ID、性能监控等中间件
"""

import time
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response as StarletteResponse
import structlog

from app.utils.logger import (
    RequestLogger, 
    generate_request_id, 
    request_id_var,
    SecurityLogger
)
from app.config import settings
from app.utils.timezone import get_china_timestamp


class RequestIDMiddleware(BaseHTTPMiddleware):
    """请求ID中间件 - 为每个请求生成唯一ID"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 生成请求ID
        request_id = generate_request_id()
        
        # 设置请求ID到上下文变量
        request_id_var.set(request_id)
        
        # 将请求ID添加到请求状态中，供其他中间件和路由使用
        request.state.request_id = request_id
        
        # 处理请求
        response = await call_next(request)
        
        # 在响应头中添加请求ID
        response.headers["X-Request-ID"] = request_id
        
        return response


class LoggingMiddleware(BaseHTTPMiddleware):
    """请求日志中间件 - 记录所有HTTP请求和响应"""
    
    def __init__(self, app):
        super().__init__(app)
        self.request_logger = RequestLogger()
        self.security_logger = SecurityLogger()
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 获取请求ID
        request_id = getattr(request.state, 'request_id', 'unknown')
        
        # 记录请求开始
        start_time = self.request_logger.log_request_start(request, request_id)
        
        # 安全检查
        self._security_checks(request)
        
        response = None
        error = None
        
        try:
            # 处理请求
            response = await call_next(request)
            
        except Exception as e:
            error = e
            # 创建错误响应
            response = StarletteResponse(
                content="Internal Server Error",
                status_code=500,
                headers={"content-type": "text/plain"}
            )
            
        finally:
            # 记录请求结束
            self.request_logger.log_request_end(
                request, response, start_time, request_id, error
            )
        
        return response
    
    def _security_checks(self, request: Request):
        """基础安全检查"""
        client_ip = self._get_client_ip(request)
        
        # 检查可疑的User-Agent
        user_agent = request.headers.get("user-agent", "").lower()
        suspicious_agents = ["bot", "crawler", "spider", "scraper"]
        
        if any(agent in user_agent for agent in suspicious_agents):
            self.security_logger.log_suspicious_activity(
                activity_type="suspicious_user_agent",
                description=f"检测到可疑的User-Agent: {user_agent}",
                client_ip=client_ip,
                severity="low"
            )
        
        # 检查大量请求头
        if len(request.headers) > 50:
            self.security_logger.log_suspicious_activity(
                activity_type="excessive_headers",
                description=f"请求包含过多头部信息: {len(request.headers)}个",
                client_ip=client_ip,
                severity="medium"
            )
        
        # 检查异常长的URL
        if len(str(request.url)) > 2000:
            self.security_logger.log_suspicious_activity(
                activity_type="long_url",
                description=f"检测到异常长的URL: {len(str(request.url))}字符",
                client_ip=client_ip,
                severity="medium"
            )
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端真实IP"""
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        if request.client:
            return request.client.host
        
        return "unknown"


class PerformanceMiddleware(BaseHTTPMiddleware):
    """性能监控中间件 - 监控请求处理时间和资源使用"""
    
    def __init__(self, app):
        super().__init__(app)
        self.logger = structlog.get_logger("performance")
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = get_china_timestamp()
        
        # 处理请求
        response = await call_next(request)
        
        # 计算处理时间
        process_time = get_china_timestamp() - start_time
        
        # 记录性能指标
        self._log_performance_metrics(request, response, process_time)
        
        # 在响应头中添加处理时间
        response.headers["X-Process-Time"] = str(round(process_time * 1000, 2))
        
        return response
    
    def _log_performance_metrics(self, request: Request, response: Response, process_time: float):
        """记录性能指标"""
        request_id = getattr(request.state, 'request_id', 'unknown')
        
        # 基础性能日志
        log_data = {
            "request_id": request_id,
            "method": request.method,
            "path": request.url.path,
            "status_code": response.status_code,
            "process_time_ms": round(process_time * 1000, 2),
            "response_size": response.headers.get("content-length", "unknown")
        }
        
        # 根据处理时间确定日志级别
        if process_time > 5.0:  # 超过5秒
            self.logger.error("请求处理时间过长", **log_data, severity="high")
        elif process_time > 2.0:  # 超过2秒
            self.logger.warning("请求处理时间较长", **log_data, severity="medium")
        elif process_time > 1.0:  # 超过1秒
            self.logger.info("请求处理时间正常", **log_data, severity="low")
        else:
            # 只在调试模式下记录快速请求
            if settings.debug:
                self.logger.debug("请求处理完成", **log_data)


class RateLimitMiddleware(BaseHTTPMiddleware):
    """简单的速率限制中间件"""
    
    def __init__(self, app, max_requests: int = 100, window_seconds: int = 60):
        super().__init__(app)
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = {}  # 在生产环境中应该使用Redis等外部存储
        self.logger = structlog.get_logger("rate_limit")
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        client_ip = self._get_client_ip(request)
        current_time = get_china_timestamp()
        
        # 清理过期记录
        self._cleanup_expired_records(current_time)
        
        # 检查速率限制
        if self._is_rate_limited(client_ip, current_time):
            self.logger.warning(
                "速率限制触发",
                client_ip=client_ip,
                max_requests=self.max_requests,
                window_seconds=self.window_seconds,
                request_id=getattr(request.state, 'request_id', 'unknown')
            )
            
            return StarletteResponse(
                content="Rate limit exceeded",
                status_code=429,
                headers={
                    "Retry-After": str(self.window_seconds),
                    "X-RateLimit-Limit": str(self.max_requests),
                    "X-RateLimit-Remaining": "0"
                }
            )
        
        # 记录请求
        self._record_request(client_ip, current_time)
        
        # 处理请求
        response = await call_next(request)
        
        # 添加速率限制头部
        remaining = self._get_remaining_requests(client_ip, current_time)
        response.headers["X-RateLimit-Limit"] = str(self.max_requests)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        
        return response
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP"""
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        if request.client:
            return request.client.host
        
        return "unknown"
    
    def _cleanup_expired_records(self, current_time: float):
        """清理过期的请求记录"""
        cutoff_time = current_time - self.window_seconds
        
        for ip in list(self.requests.keys()):
            self.requests[ip] = [
                timestamp for timestamp in self.requests[ip] 
                if timestamp > cutoff_time
            ]
            
            if not self.requests[ip]:
                del self.requests[ip]
    
    def _is_rate_limited(self, client_ip: str, current_time: float) -> bool:
        """检查是否触发速率限制"""
        if client_ip not in self.requests:
            return False
        
        cutoff_time = current_time - self.window_seconds
        recent_requests = [
            timestamp for timestamp in self.requests[client_ip] 
            if timestamp > cutoff_time
        ]
        
        return len(recent_requests) >= self.max_requests
    
    def _record_request(self, client_ip: str, current_time: float):
        """记录请求时间"""
        if client_ip not in self.requests:
            self.requests[client_ip] = []
        
        self.requests[client_ip].append(current_time)
    
    def _get_remaining_requests(self, client_ip: str, current_time: float) -> int:
        """获取剩余请求次数"""
        if client_ip not in self.requests:
            return self.max_requests
        
        cutoff_time = current_time - self.window_seconds
        recent_requests = [
            timestamp for timestamp in self.requests[client_ip] 
            if timestamp > cutoff_time
        ]
        
        return max(0, self.max_requests - len(recent_requests))
