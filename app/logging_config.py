"""
日志配置模块
配置结构化日志、文件轮转、异步日志等功能
"""

import logging
import logging.handlers
import os
import sys
from typing import Dict, Any
import structlog
from structlog.stdlib import LoggerFactory
import colorlog
from app.config import settings


def setup_logging() -> None:
    """设置应用日志配置"""
    
    # 确保日志目录存在
    os.makedirs(settings.log_dir, exist_ok=True)
    
    # 配置 structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if settings.log_format == "json" 
            else structlog.dev.Console<PERSON>enderer(colors=True),
        ],
        context_class=dict,
        logger_factory=LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # 获取根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.log_level.upper()))
    
    # 清除现有处理器
    root_logger.handlers.clear()
    
    # 配置控制台处理器
    if settings.log_console_enabled:
        console_handler = _create_console_handler()
        root_logger.addHandler(console_handler)
    
    # 配置文件处理器
    if settings.log_file_enabled:
        file_handler = _create_file_handler()
        root_logger.addHandler(file_handler)
    
    # 配置第三方库日志级别
    _configure_third_party_loggers()


def _create_console_handler() -> logging.Handler:
    """创建控制台日志处理器"""
    handler = logging.StreamHandler(sys.stdout)
    
    if settings.log_format == "json":
        formatter = logging.Formatter(
            '{"timestamp": "%(asctime)s", "level": "%(levelname)s", '
            '"logger": "%(name)s", "message": "%(message)s"}'
        )
    else:
        # 使用彩色日志格式
        formatter = colorlog.ColoredFormatter(
            "%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
            log_colors={
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'red,bg_white',
            }
        )
    
    handler.setFormatter(formatter)
    handler.setLevel(getattr(logging, settings.log_level.upper()))
    return handler


def _create_file_handler() -> logging.Handler:
    """创建文件日志处理器（支持轮转）"""
    handler = logging.handlers.RotatingFileHandler(
        filename=settings.log_file_path,
        maxBytes=settings.log_max_size,
        backupCount=settings.log_backup_count,
        encoding='utf-8'
    )
    
    if settings.log_format == "json":
        formatter = logging.Formatter(
            '{"timestamp": "%(asctime)s", "level": "%(levelname)s", '
            '"logger": "%(name)s", "module": "%(module)s", "function": "%(funcName)s", '
            '"line": %(lineno)d, "message": "%(message)s"}'
        )
    else:
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(module)s:%(funcName)s:%(lineno)d - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S"
        )
    
    handler.setFormatter(formatter)
    handler.setLevel(getattr(logging, settings.log_level.upper()))
    return handler


def _configure_third_party_loggers() -> None:
    """配置第三方库的日志级别"""
    third_party_loggers = {
        "uvicorn": "INFO",
        "uvicorn.access": "INFO",
        "uvicorn.error": "INFO",
        "fastapi": "INFO",
        "sqlalchemy": "WARNING",
        "sqlalchemy.engine": "WARNING",
        "httpx": "WARNING",
        "asyncio": "WARNING",
    }
    
    for logger_name, level in third_party_loggers.items():
        logging.getLogger(logger_name).setLevel(getattr(logging, level))


def get_logger(name: str) -> structlog.stdlib.BoundLogger:
    """获取结构化日志记录器"""
    return structlog.get_logger(name)


# 日志装饰器
def log_function_call(logger: structlog.stdlib.BoundLogger):
    """函数调用日志装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger.info(
                "函数调用开始",
                function=func.__name__,
                module=func.__module__,
                args_count=len(args),
                kwargs_keys=list(kwargs.keys())
            )
            try:
                result = func(*args, **kwargs)
                logger.info(
                    "函数调用成功",
                    function=func.__name__,
                    module=func.__module__
                )
                return result
            except Exception as e:
                logger.error(
                    "函数调用失败",
                    function=func.__name__,
                    module=func.__module__,
                    error=str(e),
                    error_type=type(e).__name__
                )
                raise
        return wrapper
    return decorator
