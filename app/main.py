"""
FastAPI 主应用文件
应用入口点，配置中间件、路由和生命周期事件
"""

from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
import time
import structlog

from app.config import settings
from app.logging_config import setup_logging
from app.middleware import LoggingMiddleware, RequestIDMiddleware
from app.api.v1.api import api_router
from app.core.exceptions import setup_exception_handlers


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger = structlog.get_logger("app.startup")
    logger.info(
        "应用启动",
        app_name=settings.app_name,
        version=settings.app_version,
        environment=settings.environment,
        debug=settings.debug
    )

    # 初始化数据库
    from app.database import init_db, check_db_connection

    if await check_db_connection():
        await init_db()
        logger.info("数据库初始化完成")
    else:
        logger.error("数据库连接失败")

    yield

    # 关闭时执行
    from app.database import close_db
    await close_db()
    logger.info("应用关闭")


def create_application() -> FastAPI:
    """创建 FastAPI 应用实例"""
    
    # 设置日志
    setup_logging()
    
    # 创建应用实例
    app = FastAPI(
        title=settings.app_name,
        description=settings.app_description,
        version=settings.app_version,
        docs_url=settings.docs_url if settings.debug else None,
        redoc_url=settings.redoc_url if settings.debug else None,
        openapi_url="/openapi.json" if settings.debug else None,
        lifespan=lifespan,
        # 中文文档配置
        openapi_tags=[
            {
                "name": "认证",
                "description": "用户认证相关接口，包括登录、登出、令牌管理等功能"
            },
            {
                "name": "用户信息",
                "description": "用户个人信息管理，包括资料查看、修改、头像上传等功能"
            },
            {
                "name": "健康检查",
                "description": "服务健康状态检查，包括基础检查、详细检查、就绪检查等"
            },
            {
                "name": "监控",
                "description": "系统监控和性能指标，包括应用指标、系统资源使用情况等"
            },
            {
                "name": "AI对话会话",
                "description": "AI对话会话管理，包括创建会话、查询会话列表、获取会话详情等功能"
            },
            {
                "name": "聊天",
                "description": "聊天功能，包括流式聊天完成、对话历史查询等功能"
            }
        ]
    )
    
    # 配置中间件
    setup_middleware(app)
    
    # 配置异常处理
    setup_exception_handlers(app)
    
    # 配置路由
    setup_routes(app)

    # 配置静态文件服务
    setup_static_files(app)

    return app


def setup_middleware(app: FastAPI) -> None:
    """配置应用中间件"""
    
    # 请求ID中间件（必须在最前面）
    app.add_middleware(RequestIDMiddleware)
    
    # 日志中间件
    app.add_middleware(LoggingMiddleware)
    
    # CORS 中间件
    if settings.cors_origins:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=settings.cors_origins,
            allow_credentials=settings.cors_allow_credentials,
            allow_methods=settings.cors_allow_methods,
            allow_headers=settings.cors_allow_headers,
        )
    
    # 可信主机中间件（生产环境）
    if settings.is_production:
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["*"]  # 在生产环境中应该配置具体的主机名
        )


def setup_routes(app: FastAPI) -> None:
    """配置应用路由"""
    
    # 包含 API v1 路由
    app.include_router(
        api_router,
        prefix=settings.api_v1_prefix
    )
    
    # 根路径健康检查
    @app.get("/", tags=["健康检查"])
    async def root():
        """根路径健康检查"""
        return {
            "message": f"欢迎使用 {settings.app_name}",
            "version": settings.app_version,
            "environment": settings.environment,
            "status": "healthy",
            "timestamp": time.time()
        }
    
    # 健康检查端点
    @app.get("/health", tags=["健康检查"])
    async def health_check():
        """详细健康检查"""
        return {
            "status": "healthy",
            "app_name": settings.app_name,
            "version": settings.app_version,
            "environment": settings.environment,
            "timestamp": time.time(),
            "uptime": "运行中",
            "database": "connected",  # 这里可以添加实际的数据库连接检查
            "cache": "connected",     # 这里可以添加实际的缓存连接检查
        }
    
    # 指标端点（如果启用）
    if settings.metrics_enabled:
        @app.get("/metrics", tags=["监控"])
        async def metrics():
            """应用指标"""
            return {
                "requests_total": "待实现",
                "requests_duration": "待实现",
                "active_connections": "待实现",
                "memory_usage": "待实现"
            }


def setup_static_files(app: FastAPI) -> None:
    """配置静态文件服务"""
    import os

    # 创建上传目录
    uploads_dir = "uploads"
    if not os.path.exists(uploads_dir):
        os.makedirs(uploads_dir)

    # 挂载静态文件目录
    app.mount("/uploads", StaticFiles(directory=uploads_dir), name="uploads")


# 创建应用实例
app = create_application()


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.reload,
        log_level=settings.log_level.lower(),
        access_log=False,  # 我们使用自定义的请求日志中间件
    )
