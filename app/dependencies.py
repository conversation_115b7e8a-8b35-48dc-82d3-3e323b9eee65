"""
全局依赖模块
定义可重用的依赖项，如数据库会话、认证、权限检查等
"""

from typing import Generator, Optional, Dict, Any
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import structlog

from app.config import settings
from app.utils.logger import BusinessLogger, SecurityLogger, get_request_id, set_user_context


# 安全相关
security = HTTPBearer(auto_error=False)
logger = structlog.get_logger("dependencies")
security_logger = SecurityLogger()


async def get_current_request_id(request: Request) -> str:
    """获取当前请求ID"""
    return getattr(request.state, 'request_id', 'unknown')


def get_database_session():
    """获取数据库会话"""
    from app.database import get_db
    return get_db()


async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    request: Request = None
) -> Optional[Dict[str, Any]]:
    """
    获取当前认证用户
    从JWT token中解析用户信息
    """
    if not credentials:
        return None

    try:
        from app.services.auth_service import AuthService

        auth_service = AuthService()
        user_info = await auth_service.verify_access_token(credentials.credentials)

        if not user_info:
            return None

        # 设置用户上下文
        set_user_context(user_info["user_id"])

        # 记录认证成功
        client_ip = _get_client_ip(request) if request else None
        security_logger.log_authentication(
            user_id=user_info["user_id"],
            success=True,
            method="jwt",
            client_ip=client_ip
        )

        return user_info

    except Exception as e:
        # 记录认证失败
        client_ip = _get_client_ip(request) if request else None
        security_logger.log_authentication(
            user_id="unknown",
            success=False,
            method="jwt",
            client_ip=client_ip,
            details={"error": str(e)}
        )

        logger.warning("Token解析失败", error=str(e), request_id=get_request_id())
        return None


async def require_authentication(
    current_user: Optional[Dict[str, Any]] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    要求用户认证
    如果用户未认证则抛出401错误
    """
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未认证，请先登录",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not current_user.get("is_active", False):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="账户已被禁用",
        )
    
    return current_user


async def require_admin(
    current_user: Dict[str, Any] = Depends(require_authentication)
) -> Dict[str, Any]:
    """
    要求管理员权限
    """
    if "admin" not in current_user.get("roles", []):
        security_logger.log_authorization(
            user_id=current_user["id"],
            resource="admin",
            action="access",
            success=False
        )
        
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要管理员权限",
        )
    
    security_logger.log_authorization(
        user_id=current_user["id"],
        resource="admin",
        action="access",
        success=True
    )
    
    return current_user


def require_permission(permission: str):
    """
    权限检查依赖工厂
    返回一个检查特定权限的依赖函数
    """
    async def permission_checker(
        current_user: Dict[str, Any] = Depends(require_authentication)
    ) -> Dict[str, Any]:
        user_permissions = current_user.get("permissions", [])
        user_roles = current_user.get("roles", [])
        
        # 检查直接权限
        has_permission = permission in user_permissions
        
        # 检查角色权限（简化实现）
        if not has_permission:
            # 管理员拥有所有权限
            has_permission = "admin" in user_roles
        
        if not has_permission:
            security_logger.log_authorization(
                user_id=current_user["id"],
                resource=permission,
                action="access",
                success=False
            )
            
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足，需要 {permission} 权限",
            )
        
        security_logger.log_authorization(
            user_id=current_user["id"],
            resource=permission,
            action="access",
            success=True
        )
        
        return current_user
    
    return permission_checker


async def get_pagination_params(
    page: int = 1,
    size: int = 20,
    max_size: int = 100
) -> Dict[str, int]:
    """
    分页参数依赖
    """
    if page < 1:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="页码必须大于0"
        )
    
    if size < 1:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="每页大小必须大于0"
        )
    
    if size > max_size:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"每页大小不能超过{max_size}"
        )
    
    return {
        "page": page,
        "size": size,
        "offset": (page - 1) * size
    }


async def get_business_logger(module_name: str = "general") -> BusinessLogger:
    """
    获取业务日志记录器
    """
    return BusinessLogger(module_name)


async def validate_content_type(request: Request, expected_type: str = "application/json"):
    """
    验证请求内容类型
    """
    content_type = request.headers.get("content-type", "")
    
    if not content_type.startswith(expected_type):
        raise HTTPException(
            status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
            detail=f"不支持的内容类型，期望: {expected_type}"
        )


def _get_client_ip(request: Request) -> str:
    """获取客户端真实IP"""
    if not request:
        return "unknown"
    
    forwarded_for = request.headers.get("x-forwarded-for")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()
    
    real_ip = request.headers.get("x-real-ip")
    if real_ip:
        return real_ip
    
    if request.client:
        return request.client.host
    
    return "unknown"


# 常用依赖组合
CommonDeps = {
    "db": Depends(get_database_session),
    "current_user": Depends(get_current_user),
    "authenticated_user": Depends(require_authentication),
    "admin_user": Depends(require_admin),
    "pagination": Depends(get_pagination_params),
    "request_id": Depends(get_current_request_id),
}
