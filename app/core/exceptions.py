"""
自定义异常和异常处理器
定义应用特定的异常类型和全局异常处理逻辑
"""

from typing import Any, Dict, Optional
from fastapi import FastAPI, Request, HTTPException, status
from fastapi.responses import J<PERSON>NResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import structlog

from app.utils.logger import get_request_id


logger = structlog.get_logger("exceptions")


# 自定义异常类
class BaseCustomException(Exception):
    """基础自定义异常类"""
    
    def __init__(
        self,
        message: str,
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class BusinessLogicError(BaseCustomException):
    """业务逻辑错误"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_400_BAD_REQUEST,
            details=details
        )


class ResourceNotFoundError(BaseCustomException):
    """资源未找到错误"""
    
    def __init__(self, resource_type: str, resource_id: str):
        message = f"{resource_type} (ID: {resource_id}) 未找到"
        super().__init__(
            message=message,
            status_code=status.HTTP_404_NOT_FOUND,
            details={"resource_type": resource_type, "resource_id": resource_id}
        )


class DuplicateResourceError(BaseCustomException):
    """资源重复错误"""
    
    def __init__(self, resource_type: str, field: str, value: str):
        message = f"{resource_type} 的 {field} '{value}' 已存在"
        super().__init__(
            message=message,
            status_code=status.HTTP_409_CONFLICT,
            details={"resource_type": resource_type, "field": field, "value": value}
        )


class AuthenticationError(BaseCustomException):
    """认证错误"""
    
    def __init__(self, message: str = "认证失败"):
        super().__init__(
            message=message,
            status_code=status.HTTP_401_UNAUTHORIZED
        )


class AuthorizationError(BaseCustomException):
    """授权错误"""
    
    def __init__(self, message: str = "权限不足"):
        super().__init__(
            message=message,
            status_code=status.HTTP_403_FORBIDDEN
        )


class ValidationError(BaseCustomException):
    """数据验证错误"""
    
    def __init__(self, message: str, field: str, value: Any = None):
        super().__init__(
            message=message,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details={"field": field, "value": value}
        )


class DatabaseError(BaseCustomException):
    """数据库操作错误"""
    
    def __init__(self, message: str, operation: str):
        super().__init__(
            message=f"数据库操作失败: {message}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details={"operation": operation}
        )


class ExternalServiceError(BaseCustomException):
    """外部服务错误"""
    
    def __init__(self, service_name: str, message: str):
        super().__init__(
            message=f"外部服务 {service_name} 错误: {message}",
            status_code=status.HTTP_502_BAD_GATEWAY,
            details={"service_name": service_name}
        )


class RateLimitError(BaseCustomException):
    """速率限制错误"""
    
    def __init__(self, message: str = "请求过于频繁，请稍后再试"):
        super().__init__(
            message=message,
            status_code=status.HTTP_429_TOO_MANY_REQUESTS
        )


# 异常处理器
async def custom_exception_handler(request: Request, exc: BaseCustomException) -> JSONResponse:
    """自定义异常处理器"""
    request_id = get_request_id()
    
    # 记录异常日志
    logger.error(
        "自定义异常",
        exception_type=type(exc).__name__,
        message=exc.message,
        status_code=exc.status_code,
        details=exc.details,
        request_id=request_id,
        path=request.url.path,
        method=request.method
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": {
                "type": type(exc).__name__,
                "message": exc.message,
                "details": exc.details,
                "request_id": request_id,
                "timestamp": str(getattr(request.state, "start_time", "unknown"))
            }
        }
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """HTTP异常处理器"""
    request_id = get_request_id()
    
    # 记录HTTP异常日志
    logger.warning(
        "HTTP异常",
        status_code=exc.status_code,
        detail=exc.detail,
        request_id=request_id,
        path=request.url.path,
        method=request.method
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": {
                "type": "HTTPException",
                "message": exc.detail,
                "status_code": exc.status_code,
                "request_id": request_id
            }
        }
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """请求验证异常处理器"""
    request_id = get_request_id()
    
    # 格式化验证错误信息
    errors = []
    for error in exc.errors():
        # 处理input字段，确保可以JSON序列化
        input_value = error.get("input")
        if isinstance(input_value, bytes):
            # 如果是bytes类型，转换为字符串表示
            input_value = f"<bytes: {len(input_value)} bytes>"
        elif hasattr(input_value, '__dict__') and not isinstance(input_value, (str, int, float, bool, list, dict, type(None))):
            # 如果是复杂对象，转换为字符串表示
            input_value = str(input_value)

        errors.append({
            "field": " -> ".join(str(loc) for loc in error["loc"]),
            "message": error["msg"],
            "type": error["type"],
            "input": input_value
        })
    
    # 记录验证异常日志
    logger.warning(
        "请求验证失败",
        errors=errors,
        request_id=request_id,
        path=request.url.path,
        method=request.method
    )
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "error": {
                "type": "ValidationError",
                "message": "请求数据验证失败",
                "errors": errors,
                "request_id": request_id
            }
        }
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """通用异常处理器"""
    request_id = get_request_id()
    
    # 记录未捕获的异常
    logger.error(
        "未捕获的异常",
        exception_type=type(exc).__name__,
        message=str(exc),
        request_id=request_id,
        path=request.url.path,
        method=request.method,
        exc_info=True
    )
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": {
                "type": "InternalServerError",
                "message": "服务器内部错误",
                "request_id": request_id
            }
        }
    )


def setup_exception_handlers(app: FastAPI) -> None:
    """设置异常处理器"""
    
    # 自定义异常处理器
    app.add_exception_handler(BaseCustomException, custom_exception_handler)
    
    # HTTP异常处理器
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    
    # 请求验证异常处理器
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    
    # 通用异常处理器（必须放在最后）
    app.add_exception_handler(Exception, general_exception_handler)
