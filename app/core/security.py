"""
安全相关功能
JWT 令牌生成和验证
"""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import jwt
import uuid
from jwt.exceptions import InvalidTokenError
import structlog

from app.config import settings
from app.utils.timezone import get_china_now, to_utc_for_db, get_expiry_time

logger = structlog.get_logger("security")


def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None, jti: Optional[str] = None) -> tuple[str, str]:
    """创建访问令牌"""
    try:
        to_encode = data.copy()

        # 生成 JTI（JWT ID）
        if not jti:
            jti = str(uuid.uuid4())

        # 设置过期时间（使用中国时区）
        china_now = get_china_now()
        if expires_delta:
            expire = china_now + expires_delta
        else:
            expire = china_now + timedelta(minutes=settings.access_token_expire_minutes)

        to_encode.update({
            "exp": expire.timestamp(),
            "iat": china_now.timestamp(),
            "jti": jti,
            "type": "access"
        })

        # 生成令牌
        encoded_jwt = jwt.encode(
            to_encode,
            settings.secret_key,
            algorithm=settings.algorithm
        )

        logger.info("访问令牌创建成功", user_id=data.get("sub"), jti=jti)
        return encoded_jwt, jti

    except Exception as e:
        logger.error("访问令牌创建失败", error=str(e))
        raise


def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """验证令牌"""
    try:
        payload = jwt.decode(
            token, 
            settings.secret_key, 
            algorithms=[settings.algorithm]
        )
        
        # 检查令牌类型
        if payload.get("type") != "access":
            logger.warning("无效的令牌类型", token_type=payload.get("type"))
            return None
        
        # 检查必需字段
        user_id = payload.get("sub")
        if not user_id:
            logger.warning("令牌缺少用户ID")
            return None
        
        logger.debug("令牌验证成功", user_id=user_id)
        return payload
        
    except jwt.ExpiredSignatureError:
        logger.warning("令牌已过期")
        return None
    except jwt.InvalidTokenError as e:
        logger.warning("无效的令牌", error=str(e))
        return None
    except Exception as e:
        logger.error("令牌验证失败", error=str(e))
        return None


def create_refresh_token(data: Dict[str, Any], jti: Optional[str] = None) -> tuple[str, str]:
    """创建刷新令牌"""
    try:
        to_encode = data.copy()

        # 生成 JTI（JWT ID）
        if not jti:
            jti = str(uuid.uuid4())

        # 刷新令牌有效期更长（180天）- 使用中国时区
        china_now = get_china_now()
        expire = china_now + timedelta(days=180)

        to_encode.update({
            "exp": expire.timestamp(),
            "iat": china_now.timestamp(),
            "jti": jti,
            "type": "refresh"
        })

        encoded_jwt = jwt.encode(
            to_encode,
            settings.secret_key,
            algorithm=settings.algorithm
        )

        logger.info("刷新令牌创建成功", user_id=data.get("sub"), jti=jti)
        return encoded_jwt, jti

    except Exception as e:
        logger.error("刷新令牌创建失败", error=str(e))
        raise


def verify_refresh_token(token: str) -> Optional[Dict[str, Any]]:
    """验证刷新令牌"""
    try:
        payload = jwt.decode(
            token, 
            settings.secret_key, 
            algorithms=[settings.algorithm]
        )
        
        # 检查令牌类型
        if payload.get("type") != "refresh":
            logger.warning("无效的刷新令牌类型", token_type=payload.get("type"))
            return None
        
        user_id = payload.get("sub")
        if not user_id:
            logger.warning("刷新令牌缺少用户ID")
            return None
        
        logger.debug("刷新令牌验证成功", user_id=user_id)
        return payload
        
    except jwt.ExpiredSignatureError:
        logger.warning("刷新令牌已过期")
        return None
    except jwt.InvalidTokenError as e:
        logger.warning("无效的刷新令牌", error=str(e))
        return None
    except Exception as e:
        logger.error("刷新令牌验证失败", error=str(e))
        return None


def get_token_payload(token: str) -> Optional[Dict[str, Any]]:
    """获取令牌载荷（不验证过期时间）"""
    try:
        payload = jwt.decode(
            token, 
            settings.secret_key, 
            algorithms=[settings.algorithm],
            options={"verify_exp": False}
        )
        return payload
    except Exception as e:
        logger.error("获取令牌载荷失败", error=str(e))
        return None


def is_token_expired(token: str) -> bool:
    """检查令牌是否过期"""
    try:
        payload = get_token_payload(token)
        if not payload:
            return True
        
        exp = payload.get("exp")
        if not exp:
            return True
        
        return get_china_now().timestamp() > exp
        
    except Exception:
        return True


def get_token_remaining_time(token: str) -> Optional[int]:
    """获取令牌剩余有效时间（秒）"""
    try:
        payload = get_token_payload(token)
        if not payload:
            return None
        
        exp = payload.get("exp")
        if not exp:
            return None
        
        remaining = exp - get_china_now().timestamp()
        return max(0, int(remaining))
        
    except Exception:
        return None
