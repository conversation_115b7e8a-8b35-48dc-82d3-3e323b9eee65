#!/usr/bin/env python3
"""
项目启动脚本
提供数据库初始化和服务器启动功能
"""

import asyncio
import sys
import argparse
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def init_database():
    """数据库初始化（已禁用）"""
    print("🔧 数据库校验已禁用，跳过数据库检查...")
    print("✅ 数据库检查已跳过")
    return True


def start_server():
    """启动服务器"""
    print("🚀 正在启动服务器...")
    
    try:
        from uvicorn_config import run_server
        run_server()
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        sys.exit(1)


def check_dependencies():
    """检查依赖"""
    print("📦 检查项目依赖...")
    
    required_packages = [
        'fastapi', 'uvicorn', 'sqlalchemy', 'pydantic',
        'structlog', 'jwt', 'aiofiles'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包已安装")
    return True


def check_database_structure():
    """检查数据库结构（已禁用）"""
    print("🔍 数据库结构检查已禁用，跳过检查...")
    print("✅ 数据库结构检查已跳过")


def fix_database_structure():
    """修复数据库结构（已禁用）"""
    print("🔧 数据库结构修复已禁用，跳过修复...")
    print("✅ 数据库结构修复已跳过")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="FastAPI 项目启动脚本")
    parser.add_argument("--init-db", action="store_true", help="初始化数据库")
    parser.add_argument("--check-deps", action="store_true", help="检查依赖")
    parser.add_argument("--check-db", action="store_true", help="检查数据库结构")
    parser.add_argument("--fix-db", action="store_true", help="修复数据库结构")
    parser.add_argument("--dev", action="store_true", help="开发模式启动")

    args = parser.parse_args()

    # 检查依赖
    if args.check_deps or not any(vars(args).values()):
        if not check_dependencies():
            sys.exit(1)

    # 检查数据库结构
    if args.check_db:
        check_database_structure()
        return

    # 修复数据库结构
    if args.fix_db:
        fix_database_structure()
        return

    # 初始化数据库（已禁用，总是成功）
    if args.init_db or not any(vars(args).values()):
        init_database()  # 总是返回True，不需要检查

    # 启动服务器
    if not any([args.check_deps, args.init_db, args.check_db, args.fix_db]):
        start_server()


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)
