"""
Uvicorn 服务器配置
提供不同环境下的服务器启动配置
"""

import os
import multiprocessing
from app.config import settings


class UvicornConfig:
    """Uvicorn 配置类"""
    
    def __init__(self):
        self.app = "app.main:app"
        self.host = settings.host
        self.port = settings.port
        self.reload = settings.reload and settings.is_development
        self.log_level = settings.log_level.lower()
        
        # 根据环境设置不同的配置
        if settings.is_production:
            self._setup_production_config()
        elif settings.is_development:
            self._setup_development_config()
        else:
            self._setup_default_config()
    
    def _setup_production_config(self):
        """生产环境配置"""
        self.workers = self._get_worker_count()
        self.worker_class = "uvicorn.workers.UvicornWorker"
        self.reload = False
        self.access_log = False  # 使用自定义日志中间件
        self.server_header = False
        self.date_header = False
        
        # SSL 配置（如果需要）
        self.ssl_keyfile = os.getenv("SSL_KEYFILE")
        self.ssl_certfile = os.getenv("SSL_CERTFILE")
        
        # 性能优化
        self.loop = "uvloop"  # 使用 uvloop 提高性能
        self.http = "httptools"  # 使用 httptools 提高 HTTP 解析性能
        
        # 连接配置
        self.limit_concurrency = 1000
        self.limit_max_requests = 10000
        self.timeout_keep_alive = 5
        self.timeout_graceful_shutdown = 30
    
    def _setup_development_config(self):
        """开发环境配置"""
        self.workers = 1
        self.reload = True
        self.reload_dirs = ["app"]
        self.reload_includes = ["*.py"]
        self.reload_excludes = ["scripts/*", "logs/*", "*.md", "*.txt", "*.json", "__pycache__/*"]
        self.access_log = True
        self.use_colors = True

        # 开发时的调试配置
        self.debug = True
    
    def _setup_default_config(self):
        """默认配置"""
        self.workers = 1
        self.access_log = True
        self.use_colors = True
    
    def _get_worker_count(self) -> int:
        """计算工作进程数量"""
        # 获取 CPU 核心数
        cpu_count = multiprocessing.cpu_count()
        
        # 从环境变量获取工作进程数，如果没有设置则使用公式计算
        worker_count = os.getenv("WORKERS")
        if worker_count:
            try:
                return int(worker_count)
            except ValueError:
                pass
        
        # 推荐的工作进程数：(2 x CPU核心数) + 1
        return min(cpu_count * 2 + 1, 8)  # 最多8个进程
    
    def get_config_dict(self) -> dict:
        """获取配置字典"""
        config = {
            "app": self.app,
            "host": self.host,
            "port": self.port,
            "log_level": self.log_level,
        }
        
        # 添加可选配置（排除 debug，uvicorn.run 不支持此参数）
        optional_configs = [
            "workers", "worker_class", "reload", "reload_dirs", "reload_includes", "reload_excludes",
            "access_log", "use_colors", "server_header", "date_header",
            "ssl_keyfile", "ssl_certfile", "loop", "http",
            "limit_concurrency", "limit_max_requests",
            "timeout_keep_alive", "timeout_graceful_shutdown"
        ]
        
        for attr in optional_configs:
            if hasattr(self, attr):
                value = getattr(self, attr)
                if value is not None:
                    config[attr] = value
        
        return config
    
    def get_gunicorn_config(self) -> dict:
        """获取 Gunicorn 配置（用于生产环境）"""
        return {
            "bind": f"{self.host}:{self.port}",
            "workers": self.workers,
            "worker_class": "uvicorn.workers.UvicornWorker",
            "worker_connections": 1000,
            "max_requests": 10000,
            "max_requests_jitter": 1000,
            "timeout": 30,
            "keepalive": 5,
            "preload_app": True,
            "accesslog": "-" if settings.is_development else None,
            "errorlog": "-",
            "loglevel": self.log_level,
            "access_log_format": '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s',
        }


def get_uvicorn_config() -> UvicornConfig:
    """获取 Uvicorn 配置实例"""
    return UvicornConfig()


def run_server():
    """运行服务器"""
    import uvicorn
    
    config = get_uvicorn_config()
    config_dict = config.get_config_dict()
    
    print(f"🚀 启动 {settings.app_name} v{settings.app_version}")
    print(f"📍 环境: {settings.environment}")
    print(f"🌐 地址: http://{config.host}:{config.port}")
    print(f"📚 API 文档: http://{config.host}:{config.port}/docs")
    print(f"🔍 ReDoc: http://{config.host}:{config.port}/redoc")
    
    if settings.is_production:
        print(f"👥 工作进程数: {config.workers}")
    
    print("=" * 50)
    
    # 启动服务器
    uvicorn.run(**config_dict)


def create_gunicorn_config_file():
    """创建 Gunicorn 配置文件"""
    config = get_uvicorn_config()
    gunicorn_config = config.get_gunicorn_config()
    
    config_content = f"""# Gunicorn 配置文件
# 自动生成，请勿手动修改

import multiprocessing
from app.config import settings

# 服务器套接字
bind = "{gunicorn_config['bind']}"
backlog = 2048

# 工作进程
workers = {gunicorn_config['workers']}
worker_class = "{gunicorn_config['worker_class']}"
worker_connections = {gunicorn_config['worker_connections']}
max_requests = {gunicorn_config['max_requests']}
max_requests_jitter = {gunicorn_config['max_requests_jitter']}
timeout = {gunicorn_config['timeout']}
keepalive = {gunicorn_config['keepalive']}
preload_app = {gunicorn_config['preload_app']}

# 日志
accesslog = {repr(gunicorn_config['accesslog'])}
errorlog = "{gunicorn_config['errorlog']}"
loglevel = "{gunicorn_config['loglevel']}"
access_log_format = '{gunicorn_config['access_log_format']}'

# 进程命名
proc_name = "{settings.app_name.lower().replace(' ', '_')}"

# 安全
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

# 服务器机制
daemon = False
pidfile = None
user = None
group = None
tmp_upload_dir = None

# SSL（如果配置了的话）
keyfile = {repr(os.getenv('SSL_KEYFILE'))}
certfile = {repr(os.getenv('SSL_CERTFILE'))}

def when_ready(server):
    server.log.info("🚀 服务器启动完成")

def worker_int(worker):
    worker.log.info("👷 工作进程中断: %s", worker.pid)

def pre_fork(server, worker):
    server.log.info("👷 启动工作进程: %s", worker.pid)

def post_fork(server, worker):
    server.log.info("👷 工作进程已启动: %s", worker.pid)

def worker_abort(worker):
    worker.log.info("👷 工作进程异常终止: %s", worker.pid)
"""
    
    with open("gunicorn.conf.py", "w", encoding="utf-8") as f:
        f.write(config_content)
    
    print("✅ Gunicorn 配置文件已生成: gunicorn.conf.py")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "run":
            run_server()
        elif command == "gunicorn-config":
            create_gunicorn_config_file()
        elif command == "config":
            # 显示当前配置
            config = get_uvicorn_config()
            print("当前 Uvicorn 配置:")
            print("=" * 30)
            for key, value in config.get_config_dict().items():
                print(f"{key}: {value}")
        else:
            print("未知命令。可用命令: run, gunicorn-config, config")
    else:
        run_server()
